import 'package:flutter/material.dart';
import 'package:ju_medicine/NotificationsHandler.dart';
import 'package:ju_medicine/pages/FirstActivity.dart';
import './components/DrawerWidget.dart';
import './pages/HomePage.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:firebase_core/firebase_core.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp();
  initializeNotifications();

  runApp(const MyApp());
}

class MyApp extends StatefulWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  _MyAppState createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      theme: ThemeData(
          backgroundColor: const Color(0xFF36A39E),
          primaryColor: const Color(0xFF36A39E)),
      home: const EntryApp(),
    );
  }
}

class EntryApp extends StatefulWidget {
  const EntryApp({Key? key}) : super(key: key);

  @override
  _EntryAppState createState() => _EntryAppState();
}

class _EntryAppState extends State<EntryApp> {
  late Future<List<String>> checkifFirstTime = _check();

  Future<List<String>> _check() async {
    SharedPreferences shared = await SharedPreferences.getInstance();
    if (shared.containsKey('site')) {
      List<String> res = await Future.delayed(
          const Duration(milliseconds: 800), () => [shared.getString('site')!]);
      if (shared.containsKey('semester_id')) {
        String semester = shared.getString('semester_id')!;
        res.add(semester);
      }
      return res;
    } else {
      List<String> res =
          await Future.delayed(const Duration(milliseconds: 800), () => []);
      return res;
    }
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
      future: checkifFirstTime,
      builder: (BuildContext context, AsyncSnapshot<List<String>> snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const SplashScreen();
        } else if (snapshot.data!.isEmpty) {
          return const FirstActivity();
        } else {
          return BuildHomePageWithDrawer(
              site: snapshot.data![0],
              semester: snapshot.data!.length == 2 ? snapshot.data![1] : null);
        }
      },
    );
  }
}

class BuildHomePageWithDrawer extends StatefulWidget {
  final String site;
  final String? semester;
  const BuildHomePageWithDrawer({Key? key, required this.site, this.semester})
      : super(key: key);

  @override
  _BuildHomePageWithDrawerState createState() =>
      _BuildHomePageWithDrawerState();
}

class _BuildHomePageWithDrawerState extends State<BuildHomePageWithDrawer> {
  int activeIndex = 0;
  final GlobalKey<HomePageState> _key = GlobalKey();
  void changeActive(int newIndex) {
    setState(() {
      activeIndex = newIndex;
    });
    _key.currentState?.closeDrawer();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      backgroundColor: Theme.of(context).primaryColor,
      body: Stack(
        children: [
          DrawerWidget(activeIndex: activeIndex, changeActive: changeActive),
          SafeArea(
            child: HomePage(
              key: _key,
              site: widget.site,
              semester: widget.semester,
              activeIndex: activeIndex,
            ),
          ),
        ],
      ),
    );
  }
}

class SplashScreen extends StatelessWidget {
  const SplashScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: SafeArea(
      child: Container(
        alignment: Alignment.center,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Image.asset(
              "assets/splash_logo.png",
              width: 240,
            ),
            Container(
                margin: EdgeInsets.only(top: 50, bottom: 15),
                child: Text("POWERED BY",
                    style: TextStyle(
                        color: Colors.black54,
                        letterSpacing: 4,
                        fontSize: 14,
                        fontWeight: FontWeight.w500))),
            Image.asset(
              "assets/lejan_logo.png",
              width: 80,
            )
          ],
        ),
      ),
    ));
  }
}
