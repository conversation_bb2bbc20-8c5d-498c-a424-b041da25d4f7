import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart';
import 'dart:io' as io;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/services.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

/*
***************************************
****Notification Handling Strategy*****
***************************************
1- in FirstActivity File
  Ask for Permission (Mainly For IOS, Android doesnt need it)
  if is it first time then subscribe to new topic (topic is the batch)
  else if it is in settings subcscribe to new bactch and unsubribe from old topic

2- inside Main inititializeNotification to onMessage(ForeGround) and Background
  to save new notification in SQLlite and increase number of unread notifications in 
  sharedPreferences ... this number will be set to zero on initState of Notification page

3- on Notification Page, onMessage will cause refreshing after one second(giving the one in main app to take time)
  ... there's another approach I am thinking of that if new message isnt found in the databse we make like recursion that rechecks but it might 
  cause infinite loop so it needs to be studied  
*/

String table = 'notifications';

const AndroidNotificationChannel channel = AndroidNotificationChannel(
  'high_importance_channel', // id
  'High Importance Notifications', // title
  importance: Importance.max,
  playSound: true,
);   

@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp();
  await handleNotifications(message);
}




Future<void> initializeNotifications() async {
 FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();

 await flutterLocalNotificationsPlugin
  .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
  ?.createNotificationChannel(channel);
 
 
  FirebaseMessaging.instance.getInitialMessage();
  FirebaseMessaging.onMessage.listen((message) async {
    RemoteNotification? notification = message.notification;

    // If `onMessage` is triggered with a notification, construct our own
    await handleNotifications(message);
    // local notification to show to users using the created channel.
    if (notification != null && io.Platform.isAndroid) {
      print("entered");
      flutterLocalNotificationsPlugin.show(
          notification.hashCode,
          notification.title,
          notification.body,
          NotificationDetails(
            android: AndroidNotificationDetails(
              channel.id,
              channel.name,
              // other properties...
            ),
          ));
    }
  });
  FirebaseMessaging messaging = FirebaseMessaging.instance;
  
  NotificationSettings settings = await messaging.requestPermission(
    alert: true,
    announcement: false,
    badge: true,
    carPlay: false,
    criticalAlert: false,
    provisional: false,
    sound: true,
  );
  //This is for IOS
  FirebaseMessaging.instance.setForegroundNotificationPresentationOptions(
    alert: true,
    sound: true,
    badge: true
  );

  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
}
Future<void> handleNotifications(RemoteMessage message) async {
  String title = message.notification!.title == null? 'JU Medicine': message.notification!.title!;
  String body = message.notification!.body == null? 'Notification': message.notification!.body!;
  await addNotificationToDB({"title":title, "body":body});
  await setUnread();
  HapticFeedback.vibrate();
  
}

Future<void> addNotificationToDB(Map<String, dynamic> row) async {
  Database database =  await initDatabase();
  await insert(database, row);
}

Future<void> setUnread({bool toZero = false}) async {
  SharedPreferences pref = await SharedPreferences.getInstance();
  int? unread = pref.getInt('unread'); 
  if(pref.getInt('unread') == null ){
    pref.setInt('unread', toZero? 0 : 1);
  } else {
    pref.setInt('unread', toZero? 0 : unread! + 1);
  }
}
Future<int> getUnread() async {
  SharedPreferences pref = await SharedPreferences.getInstance();
  int? unread = pref.getInt('unread'); 
  return unread ?? 0;
}
Future<List<Map<String, dynamic>>> queryAllRows() async {
  Database database =  await initDatabase();
  List<Map<String, dynamic>> result = await database.query(table);
  return result.reversed.toList();
}

Future<int> insert(Database db, Map<String, dynamic> row) async {
  return await db.insert(table, row);
}

// this opens the database (and creates it if it doesn't exist)
Future<Database> initDatabase() async {
  io.Directory dbDirectory;
  if (io.Platform.isIOS) {
    dbDirectory = await getLibraryDirectory();
  } else {
    dbDirectory = await getApplicationDocumentsDirectory();
  }
  String path = join(dbDirectory.path, 'notifications.db');
  
  return await openDatabase(
    path,
    version: 1,
    onCreate: onCreate
  );
}

//create db
Future<void> onCreate(Database db, int version) async {
  await db.execute('''
        CREATE TABLE notifications (
          id integer primary KEY,
          title text not null,
          body text not null
        )
        '''
  );
}