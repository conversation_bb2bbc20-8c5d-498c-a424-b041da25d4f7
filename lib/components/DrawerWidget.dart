import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:url_launcher/url_launcher.dart';
class DrawerWidget extends StatelessWidget {
  final int activeIndex;
  final void Function(int) changeActive;
  const DrawerWidget({Key? key, required this.activeIndex, required this.changeActive}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final double screenHeight = MediaQuery.of(context).size.height;
    final double screenWidth = MediaQuery.of(context).size.width;
    return SafeArea(
      child: Stack(
        children: [
          Container(
            height: screenHeight,
            width: screenWidth,
            alignment: Alignment.centerLeft,
            padding: const EdgeInsets.only(top: 0, left: 15),
            color:Theme.of(context).colorScheme.surface,
            child: SingleChildScrollView(      
                child: Column(
                  children: DrawerItems.all.map((item) => 
                  Material(
                    color: Colors.transparent,
                    child: InkWell(
                      borderRadius:  BorderRadius.circular(16),
                      onTap:(){
                        if (item.id == 3){
                          _luanchRecordsUrl(context);
                        } 
                        else {
                          changeActive(item.id);
                        }
                      },
                      splashColor: Colors.white38,
                      child: Container(
                        constraints: const BoxConstraints(maxWidth: 250 ),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(16),
                          color: activeIndex == item.id? Colors.white : Colors.transparent,
                        ),  
                        child: ListTile(
                          dense: true,
                          contentPadding: const EdgeInsets.symmetric(horizontal: 22, vertical: 4),
                          leading: Icon(item.icon, color: activeIndex == item.id? Theme.of(context).colorScheme.surface : Colors.white),
                          title: Text(
                            item.title,
                            style: TextStyle(
                              color: activeIndex == item.id? Theme.of(context).colorScheme.surface : Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ),
                    )
                  )
                ).toList()
                ),
              ),
            ),
            Positioned(
              bottom: 20.0,
              left: 40.0,
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  borderRadius:  BorderRadius.circular(16),
                  onTap:(){
                    launch('https://medlearn-jo.com/');
                  },
                  child: Container(
                    constraints: const BoxConstraints(maxWidth: 270 ),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(16),
                      color: Colors.white ,
                    ),  
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 10.0, horizontal: 12.0),
                      child: Image.asset(
                        "assets/medlearn.png",
                        width: 130,
                      ),
                    )
                  ),
                )
              )

            )
        ],
      )
    ); 
  }
  void _luanchRecordsUrl(BuildContext context) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();  
    
    String? site = prefs.getString('site');
    if (site == null){
      _luanchToast("Please Select A Batch", context);
    } else {
      String api = site + '/wp-content/themes/lejan_new/mobileApi2017/general_settings.php';
      http.Response response = await http.get(Uri.parse(api));
      
      Map<String, dynamic> jsonData = json.decode(response.body) as Map<String, dynamic>;

      if (!await launch(jsonData['one_drive'])) _luanchToast("There's no record link", context);
    }
  }
  void _luanchToast(String content,BuildContext context){
    final scaffold = ScaffoldMessenger.of(context);
    scaffold.showSnackBar(
      SnackBar(
        content: Text(content),
        action: SnackBarAction(label: 'OK', onPressed: scaffold.hideCurrentSnackBar),
      ),
    );
  }
}

class DrawerItem {
  final int id;
  final String title;
  final IconData icon;

  const DrawerItem({required this.id ,required this.title, required this.icon });
}

class DrawerItems {
  static const DrawerItem home = DrawerItem(id: 0, title: 'Home', icon: FontAwesomeIcons.home);
  static const DrawerItem yearList = DrawerItem(id: 1, title: 'Batch List', icon: FontAwesomeIcons.list);
  static const DrawerItem downloads = DrawerItem(id: 2, title: 'Downloaded Files', icon: Icons.download_sharp);
  static const DrawerItem records = DrawerItem(id: 3, title: 'Records', icon: FontAwesomeIcons.soundcloud);
  static const DrawerItem settings = DrawerItem(id: 4, title: 'Settings', icon: FontAwesomeIcons.cog);
  static const DrawerItem about = DrawerItem(id: 5, title: 'About Us', icon: FontAwesomeIcons.info);
  
  static final List<DrawerItem> all = [
    home,
    yearList,
    downloads,
    records,
    settings,
    about
  ];
}
