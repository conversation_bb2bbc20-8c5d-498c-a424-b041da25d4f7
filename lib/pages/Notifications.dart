import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:ju_medicine/NotificationsHandler.dart';

class Notifications extends StatefulWidget {
  const Notifications({ Key? key }) : super(key: key);

  @override
  State<Notifications> createState() => _NotificationsState();
}

class _NotificationsState extends State<Notifications>{
  late List<Map<String, dynamic>> notifications = [];
  @override
  void initState() {
    super.initState();
    
    refresh();
    //when this page is opened getlist(state) and zero the
    FirebaseMessaging.onMessage.listen((message) {
      if (mounted) refresh();
    });
  }


  void refresh(){
    queryAllRows().then((value){
      setState(() {
        notifications = value;
      });
      setUnread(toZero: true);
    });
  }
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        title: const Text('Notifications', maxLines: 1, overflow: TextOverflow.ellipsis, style: TextStyle(color: Colors.white, ),),
        shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.only(bottomLeft: Radius.circular(28), bottomRight: Radius.circular(28))),
        backgroundColor: Theme.of(context).colorScheme.background,
        leading: IconButton(
          iconSize: 30,
          padding: EdgeInsets.only(left: 10),
          icon: const Icon(Icons.arrow_back),
          color: Colors.white,
          onPressed: (){Navigator.pop(context);},
        ),
      ),
      body: SafeArea(child: notificationList(notifications),)
    );
  }
  Widget notificationList(List<Map<String, dynamic>> notifications) =>
    ListView(
      children: notifications.map((notification) => notificationItem(notification)).toList()
    );

  Widget notificationItem(Map<String, dynamic> notification) =>
    Container(
      padding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 26),
      margin: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 18),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(notification['title']!, style: style(false),),
          const SizedBox(height: 6,),
          Text(notification['body']!, style: style(true),)
        ],
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.15),
            spreadRadius: 5,
            blurRadius: 7,
            offset: const Offset(0, 2), // changes position of shadow
          ),
        ],
      ),
    );

  TextStyle style(bool big)=> TextStyle(
    fontSize: big? 18: 14, 
    fontWeight: big? FontWeight.w500:FontWeight.w400,
    color: big? Colors.black: Colors.black54,
  );
}