import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import './segments/CourseListSegment.dart';
import './FirstActivity.dart';
import './segments/Settings.dart';
import './About.dart';
import './Downloaded.dart';
import 'Notifications.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:ju_medicine/NotificationsHandler.dart';

class HomePage extends StatefulWidget {
  final int drawerAnimationDuration = 250, activeIndex; 
  final String site;
  final String? semester;
  const HomePage({ Key? key, required this.site, this.activeIndex = 0, this.semester}) : super(key: key);

  @override
  HomePageState createState() => HomePageState();
}

class HomePageState extends State<HomePage> with SingleTickerProviderStateMixin {
  late AnimationController hamburgerAnimation ;
  late double xOffset;
  late double yoffset;
  late double scaleFactor;
  late bool isDrawerOpen;
  bool isDragging = false;
  int unread = 0;

  closeDrawer(){
    setState(() {
      xOffset = 0;
      yoffset = 0;
      scaleFactor = 1; 
    });
    isDrawerOpen = false;
    hamburgerAnimation.forward();
  }
  openDrawer(){
    setState(() {
      xOffset = 300;
      yoffset = 150;
      scaleFactor = 0.6; 
    });
      isDrawerOpen = true;
      hamburgerAnimation.reverse();
  }

  @override 
  void initState(){
    super.initState();
    getUnread().then((value){
      setState(() {
        unread = value;
      });
    });
    FirebaseMessaging.onMessage.listen((event) {
      if (mounted) {
          setState(() {
            unread= unread+1;
          });
      }
    });
    hamburgerAnimation = AnimationController(
      vsync: this,
      duration: Duration(milliseconds: widget.drawerAnimationDuration + 100)
    );
    closeDrawer();
  }

  @override
  Widget build(BuildContext context) => GestureDetector(
      behavior: HitTestBehavior.deferToChild,
      onHorizontalDragStart: (details) => isDragging = true,
      onHorizontalDragUpdate: (details) {
        if (!isDragging ) return;
        const int delta = 2;
        if (details.delta.dx > delta){
          openDrawer();
        } else if (details.delta.dx < -delta) {
          closeDrawer();
        }

        isDragging = false;
      },
      onTap: closeDrawer,
      child: AnimatedContainer(
        duration: Duration(milliseconds: widget.drawerAnimationDuration),
        transform: Matrix4.translationValues(xOffset, yoffset, 0)
              ..scale(scaleFactor),
        child: ClipRRect(
          clipBehavior: Clip.antiAliasWithSaveLayer,
          borderRadius: BorderRadius.circular(isDrawerOpen? 28 : 0),
          child: AbsorbPointer(
            absorbing: isDrawerOpen,
            child: Scaffold(
              appBar: AppBar(
                centerTitle: true,
                title: Image.asset(
                 'assets/logo.png',
                  fit: BoxFit.contain,
                  height: 32,
                ),
                actions: [
                  badge(unread)
                ],
                shape: const RoundedRectangleBorder(
                  borderRadius: BorderRadius.only(bottomLeft: Radius.circular(28), bottomRight: Radius.circular(28))
                ),
                backgroundColor: Theme.of(context).colorScheme.surface,
                leading: IconButton(
                  iconSize: 30,
                  padding: const EdgeInsets.only(left: 16),
                  icon: AnimatedIcon(
                    icon: AnimatedIcons.close_menu,
                    progress: hamburgerAnimation,
                  ),
                  color: Colors.white,
                  onPressed: () => openDrawer(),
                ),
              ),
              body: fragment(widget.activeIndex),
            ),
          )
        ),
      ),
    );

  Widget badge(int n) => Stack(
    children: [
      IconButton(
        icon: const Icon(FontAwesomeIcons.bell),
        iconSize: 26,
        padding: const EdgeInsets.only(right: 16),
        onPressed: () async {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (BuildContext context)=> const Notifications())).then((value){
                //refresh number of unread notification
                setState(() {
                  unread = 0;
                });
              });
        },
      ),
      if (n != 0) Positioned(
        right: 12,
        top: 2,
        child: Container(
          width: 25,
          height: 25,
          alignment: Alignment.center,
          decoration: const BoxDecoration(
            shape: BoxShape.circle,
            color: Colors.redAccent,
          ),
          child: Text(unread.toString(), textAlign: TextAlign.center,style: TextStyle(fontSize: 12),),
        ),
      )
    ],
  );

    Widget fragment(int i){
      switch(i){
        case 0 : {
          return CourseList(site: widget.site, semester: widget.semester);
        }
        case 1 : {
          return const FirstActivity(isSettings: true,);
        }
        case 2 : {
          return const Downloaded();
        }
        case 4 : {
          return Settings(site: widget.site);
        }
        case 5 : {
          return const About();
        }
      }
      return const Center(child:Text("Error", style: TextStyle(fontSize: 24),));
    }
}
/*
TextFormField(
                    decoration: InputDecoration(
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(10),
                        borderSide: BorderSide(color: Colors.transparent)
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(10),
                        borderSide: BorderSide(color: Colors.transparent)
                      ),
                      prefixIcon: Icon(
                        Icons.search_off_outlined,
                        color: Theme.of(context).colorScheme.surface,
                      ),
                      hintText: 'Search...',
                      hintStyle: TextStyle(color: Theme.of(context).colorScheme.surface),
                      filled: true,
                      fillColor: Color(0xFF75CCC9),
                    ),
                  ),
*/