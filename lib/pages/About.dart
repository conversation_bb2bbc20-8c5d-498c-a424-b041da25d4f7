import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';

class About extends StatelessWidget {
  const About({ Key? key }) : super(key: key);
  final String lejan = "فريق لجان الدفعات في كلية الطب ينبثق من فكرة أن الإنسان هو خليفة الله في الأرض، يهدف إلى صناعة الطبيب الواعي، صاحب الفكرة والمشروع، ويشمل عمله على: العمل الأكاديمي: ويشمل تنظيم كتابة المحاضرات، وعقد محاضرات المراجعة والدورات التعريفية التي تهم طالب الطب، والعمل كحلقة وصل بين الطالب وأعضاء الهيئة التدريسية. العمل الاجتماعي: ويشمل إيجاد أجواء اجتماعية مميزة بين أفراد الدفعات من خلال أنشطة متنوعة، طبيةً كانت أو ترفيهيةً.";
  final String shefa = "قائمة شفاء وليدة دفعة 2015 (شفاء)، حمل كل كوادرها على عاتقهم خدمة طلاب كليتهم بمختلف سنواتهم الدراسية، والعمل على تحصيل كافة حقوقهم من خلال خوض تجربة اتحاد طلبة كلية الطب لعام 2017، مُمَثلينَ بأعضاء الاتحاد: \nعُمر إبراهيم أبوريش و يُوسف جمال عيد";
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding:const EdgeInsets.symmetric(vertical: 25, horizontal: 30),
        child:  Directionality(
          textDirection:TextDirection.rtl,
          child: Column(
            children: [
              Image.asset(
                "assets/lejan_logo.png",
                width:150,
              ),
              paragrpah(lejan),
              SizedBox(height: 30,),
              Image.asset(
                "assets/shefa_logo.png",
                width:150,
              ),
              paragrpah(shefa),
              SizedBox(height: 50),
              const Text(
                "2022 \u00a9 Designed By Mohammad Zawahreh\n Powered By Lejan Al-Dofa'at",
                textAlign: TextAlign.center,
                style: TextStyle(
                    color: Colors.black54,
                    fontWeight: FontWeight.w500,
                    fontSize: 16
                  ),
                ),
            ],
          ),
        ) 
      ),
    );
  }
  Widget paragrpah(String p) => Text(
    p,
    textAlign: TextAlign.center,
    style: const TextStyle(
        color: Colors.black87,
        fontWeight: FontWeight.w400,
        fontSize: 18
      ),
    );
}