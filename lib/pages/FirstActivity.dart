
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../main.dart';

class FirstActivity extends StatefulWidget {
  final bool isSettings;
  const FirstActivity({ Key? key, this.isSettings= false }) : super(key: key);

  @override
  _FirstActivityState createState() => _FirstActivityState();
}

class _FirstActivityState extends State<FirstActivity> {
  dynamic batchValue ;
  late List<dynamic> listOfBatches;
  late Future< List<dynamic> > yearsListFuture = _loadYears(); 

  Future< List<dynamic> > _loadYears() async{
    try {    
        const api = 'https://jumedicine.com/wp-content/themes/lejan_new/mobileApi2017/general_settings.php';
        http.Response response = await http.get(Uri.parse(api));
        Map<String, dynamic> jsonData = json.decode(response.body) as Map<String, dynamic>;
        listOfBatches = jsonData['sites'];
        return jsonData['sites'];
      } catch (e) {
        return Future.error(e);
      }
    }


  @override
  void initState(){
    super.initState();
    if (!widget.isSettings) {
      FirebaseMessaging.instance.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        padding: const EdgeInsets.only(left:45.0, right: 45.0),
        alignment: Alignment.center,
        child: FutureBuilder(
          future: yearsListFuture,
          builder:(BuildContext context, AsyncSnapshot<List<dynamic> >snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting){
              return const CircularProgressIndicator();
            } else {
              return Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (!widget.isSettings) Image.asset(
                    "assets/splash_logo.png",
                    width:240,
                  ),
                  Container(
                    margin: const EdgeInsets.only(bottom: 16),
                    padding: const EdgeInsets.symmetric(horizontal:14),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(16.0),
                      color: Theme.of(context).colorScheme.surface,
                    ),
                    child: DropdownButton(
                      iconEnabledColor: Colors.white,
                      underline: const SizedBox(),
                      isExpanded: true,
                      hint: Text("Select A Batch", style: textStyle()),
                      value: batchValue,
                      items: snapshot.data?.map((item) => DropdownMenuItem(
                        value: item['siteUrl'],
                        child: Text(item['SiteName'], style: textStyle()))
                      ).toList(),
                      borderRadius: BorderRadius.circular(24.0),
                      dropdownColor: Theme.of(context).colorScheme.surface,
                      onChanged: (dynamic value){
                        if (!widget.isSettings) { setBatch(context, value, ); }
                        else { setState(() {
                          // and save button will set state
                          batchValue = value;
                        });}
                      }
                    ),
                  ),
                  if (widget.isSettings && batchValue != null ) 
                    TextButton(
                      onPressed: (){
                        setBatch(context, batchValue);
                      },
                      style: ButtonStyle(
                        padding: MaterialStateProperty.all<EdgeInsets>(const EdgeInsets.symmetric(vertical: 2, horizontal: 12)),
                        backgroundColor: MaterialStateProperty.all<Color>(Theme.of(context).colorScheme.surface),
                        shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                          RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12.0),
                          )
                        ),
                      ),
                      child: Padding(padding: const EdgeInsets.all(8) ,child:Text("Save", style: textStyle(),))
                    )
                ],
              );
            }           
          },
        ),
        ),
    );
  }
  
  TextStyle textStyle() => const TextStyle(
      color: Colors.white,
      fontSize: 16.0,
      fontWeight: FontWeight.w500
    );

  void setBatch(BuildContext context, dynamic value) {
    SharedPreferences.getInstance().then((pref) async {
      // subscribe to new to new year and unsubscribe from old year if it is in setttings (first time there is no previous year)
      FirebaseMessaging messaging = FirebaseMessaging.instance;
      String topic = '';
      for (var element in listOfBatches) {
        if (element['siteUrl'] == value){
          topic = element['SiteName'].toString().replaceAll(' ', '').toLowerCase();
        }
      }
      messaging.unsubscribeFromTopic('null');

      messaging.subscribeToTopic(topic);
      if (widget.isSettings){
        messaging.unsubscribeFromTopic(pref.getString('topic')!);
      }
      //set new site
      pref.setString("site", value);
      pref.setString("topic", topic);
      //reset all semeseter 
      pref.remove("year");
      pref.remove("semester_id");

      //redirect to app
      if (widget.isSettings){
        Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => const EntryApp()));
      }
      else{
        Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => BuildHomePageWithDrawer(site: value)));
      }
    });
  }


}