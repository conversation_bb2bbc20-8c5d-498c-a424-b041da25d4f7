import 'package:flutter/material.dart';
import 'package:ju_medicine/FileOperations.dart';
import './postpage/PostPage.dart';
import 'dart:io' as io;

class Downloaded extends StatefulWidget {
  const Downloaded({Key? key}) : super(key: key);


  @override
  State<Downloaded> createState() => _DownloadedState();
}

class _DownloadedState extends State<Downloaded> {
  late Future<List<Map<String, dynamic>>> downloadedList = _loadDownloaded();
  Future<List<Map<String, dynamic>>> _loadDownloaded() async{
    
    io.Directory dir = await getDownloadDir();
    if (!dir.existsSync()) return [];

    List<io.FileSystemEntity> filesList = dir.listSync();
    List<Map<String, dynamic>> result = [];
    for(io.FileSystemEntity file in filesList){
      if (file is io.File){
        String name = file.uri.pathSegments.last; 
        var entry = {"id": name, "name": name.substring(0,name.lastIndexOf('.')), "file" :file.path, "secondAction": "Delete"};
        result.add(entry);
      }
    }
    return result;
  }
  void update(){
    setState(() {
      downloadedList = _loadDownloaded();
    });
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<List<Map<String, dynamic>>>(
              future: downloadedList,
              builder: (BuildContext context, AsyncSnapshot<List<Map<String, dynamic>>> snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting){
                  return const Center(child: CircularProgressIndicator());
                } else {
                  if (snapshot.data!.isEmpty) return const Center(child: Text("No Downloaded Files",style: TextStyle(fontSize: 16 , color: Colors.black87),),);
                  return Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 28.0),
                    child: Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: snapshot.data!.map((item) => popupCard(item: item, update: update)).toList(),
                    ),
                  );
            }
          },
        );
  }

}