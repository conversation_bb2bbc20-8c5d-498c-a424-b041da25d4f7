// ignore_for_file: unnecessary_new

import 'dart:io' show Platform;
import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:open_filex/open_filex.dart';
import 'package:android_intent_plus/android_intent.dart';
import 'dart:io' as io;
import 'package:dio/dio.dart';
import 'package:media_scanner/media_scanner.dart';
import 'package:ju_medicine/FileOperations.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:url_launcher/url_launcher_string.dart';

var mimeTypes = {
    "3gp":    "video/3gpp",
    "csv":    "application/vnd.ms-excel",
    "doc":    "application/msword",
    "docx":   "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "xls":    "application/vnd.ms-excel",
    "xlsx":   "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    "htm":    "text/html",
    "html":   "text/html",
    "jpeg":   "image/jpeg",
    "jpg":    "image/jpeg",
    "m3u":    "audio/x-mpegurl",
    "m4a":    "audio/mp4a-latm",
    "m4b":    "audio/mp4a-latm",
    "m4p":    "audio/mp4a-latm",
    "m4u":    "video/vnd.mpegurl",
    "m4v":    "video/x-m4v",
    "mov":    "video/quicktime",
    "mp2":    "audio/x-mpeg",
    "mp3":    "audio/x-mpeg",
    "mp4":    "video/mp4",
    "mpc":    "application/vnd.mpohun.certificate",
    "mpe":    "video/mpeg",
    "mpeg":   "video/mpeg",
    "mpg":    "video/mpeg",
    "mpg4":   "video/mp4",
    "mpga":   "audio/mpeg",
    "pdf":    "application/pdf",
    "png":    "image/png",
    "pps":    "application/vnd.ms-powerpoint",
    "ppt":    "application/vnd.ms-powerpoint",
    "pptx":   "application/vnd.openxmlformats-officedocument.presentationml.presentation",
    "txt":    "text/plain",
    "zip":    "application/x-zip-compressed",
};

String? getMimeType(String url){
  String? fileName = url.split('/').last;
  String? extention= fileName.split('.').last;
  return mimeTypes[extention];
}
class CustomRectTween extends RectTween {
  CustomRectTween({
    required Rect begin,
    required Rect end,
  }) : super(begin: begin, end: end);

  @override
  Rect lerp(double t) {
    final elasticCurveValue = Curves.easeOut.transform(t);
    final double? left = lerpDouble(begin!.left, end!.left, elasticCurveValue);
    final double? top = lerpDouble(begin!.top, end!.top, elasticCurveValue);
    final double? right =
        lerpDouble(begin!.right, end!.right, elasticCurveValue);
    final double? bottom =
        lerpDouble(begin!.bottom, end!.bottom, elasticCurveValue);
    return Rect.fromLTRB(left!, top!, right!, bottom!);
  }
}

class HeroDialogRoute<T> extends PageRoute<T> {
  late bool dismiss;
  HeroDialogRoute({
    required WidgetBuilder builder,
    RouteSettings? settings,
    bool fullscreenDialog = false,
    this.dismiss = true,
  })  : _builder = builder,
        super(settings: settings, fullscreenDialog: fullscreenDialog);

  final WidgetBuilder _builder;

  @override
  bool get opaque => false;

  @override
  bool get barrierDismissible => dismiss;

  @override
  Duration get transitionDuration => Duration(milliseconds: !dismiss ? 0 : 450);

  @override
  bool get maintainState => true;

  @override
  Color get barrierColor => Colors.black54;

  @override
  Widget buildTransitions(BuildContext context, Animation<double> animation,
      Animation<double> secondaryAnimation, Widget child) {
    return child;
  }

  @override
  Widget buildPage(BuildContext context, Animation<double> animation,
      Animation<double> secondaryAnimation) {
    return _builder(context);
  }

  @override
  String get barrierLabel => 'Popup dialog open';
}

class PostPopupCard extends StatelessWidget {
  final Map<String, dynamic> post;
  const PostPopupCard({Key? key, required this.post}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 38),
      child: Hero(
        tag: post["id"],
        createRectTween: (begin, end) {
          return CustomRectTween(begin: begin!, end: end!);
        },
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 5.0),
          child: Material(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(12),
              child: Wrap(
                clipBehavior: Clip.hardEdge,
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(
                        vertical: 15.0, horizontal: 20),
                    child: Text(
                      post["name"],
                      style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16.0,
                          fontWeight: FontWeight.w500),
                      textAlign: TextAlign.left,
                      maxLines: null,
                      overflow: TextOverflow.visible,
                    ),
                  ),
                  Padding(
                      padding: const EdgeInsets.only(
                          right: 20.0, left: 20.0, top: 22, bottom: 8),
                      child: Row(
                        mainAxisAlignment: post['secondAction'] != null
                            ? MainAxisAlignment.spaceBetween
                            : MainAxisAlignment.end,
                        children: [
                          if (post['secondAction'] != null)
                            actionButtons(post['secondAction'], post['file'],
                                id: post['id'], context: context),
                          actionButtons("Open", post['file'],
                              secondAction: post['secondAction'])
                        ],
                      ))
                ],
              )),
        ),
      ),
    );
  }

  Widget actionButtons(String action, String fileUrl,
      {String? secondAction, String? id, BuildContext? context}) {
    //when you click on open you need to know if it is Downloads or URL so I added second activity
    return TextButton(
        onPressed: () async {
          if (action == "Open") {
            if (secondAction == null || secondAction == "Download") {
              if (Platform.isAndroid) {
                AndroidIntent intent = AndroidIntent(
                  action: 'action_view',
                  data: fileUrl.trim(),
                  type: getMimeType(fileUrl)
                );
                await intent.launchChooser('Open With: ');
              } else {
                await launchUrlString(fileUrl.trim(), mode: LaunchMode.externalApplication);
              }

            } else {
              OpenFilex.open(fileUrl);
            }
          } else if (action == "Download" && context != null) {
            Navigator.of(context).push(
              HeroDialogRoute(
                dismiss: false,
                builder: (context) => DownloadDialogue(post: post),
              ),
            );
          } else if (action == "Delete") {
            io.File file = io.File(fileUrl);
            if (file.existsSync()) {
              file.delete().then((value) {
                Navigator.of(context!).pop(true);
              }).catchError((e) {
                showDialog(
                  context: context!,
                  builder: (context) => AlertDialog(
                    title: const Text('Error'),
                    content: const Text(
                        "You can't delete this file from here\n try deleting it from Files Browser."),
                    actions: <Widget>[
                      TextButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                        },
                        child: const Text(
                          'OK',
                          style: TextStyle(fontSize: 16),
                        ),
                      )
                    ],
                  ),
                );
              });
            }
          }
        },
        style: ButtonStyle(
          padding: MaterialStateProperty.all<EdgeInsets>(
              EdgeInsets.symmetric(vertical: 2, horizontal: 12)),
          backgroundColor: MaterialStateProperty.all<Color>(Color(0xFFDDDDDD)),
          shape: MaterialStateProperty.all<RoundedRectangleBorder>(
              RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12.0),
          )),
        ),
        child: Text(action,
            style: TextStyle(
                color: Color(action == "Delete" ? 0xFFF46161 : 0xFF36A39E),
                fontSize: 16.0)));
  }
}

class DownloadDialogue extends StatefulWidget {
  final Map<String, dynamic> post;
  const DownloadDialogue({Key? key, required this.post}) : super(key: key);

  @override
  _DownloadDialogueState createState() => _DownloadDialogueState();
}

class _DownloadDialogueState extends State<DownloadDialogue> {
  late double progress;
  CancelToken token = CancelToken();
  late io.Directory dir;
  late String fileName;
  late String downloadPath;
  Dio dio = Dio();

  @override
  void initState() {
    super.initState();

    progress = 0;
    Future.delayed(Duration.zero, () async {
      var status = await Permission.storage.status;
      print(status.isGranted);
      //await Permission.storage.request();
      dir = await getDownloadDir();

      //if(!dir.existsSync()) dir.createSync();
      fileName = widget.post['name'] +
          widget.post['file']
              .toString()
              .substring(widget.post['file'].toString().lastIndexOf('.'));
      downloadPath = dir.path + fileName;
      downloadFile(context);
    });
  }

  Future<void> downloadFile(BuildContext context) async {
    try {
      await dio.download(widget.post['file'], downloadPath, cancelToken: token,
          onReceiveProgress: (rec, total) {
        setState(() {
          progress = (rec / total);
        });
        if (progress == 1) {
          if (Platform.isAndroid) {
            MediaScanner.loadMedia(path: downloadPath);
            Navigator.of(context).pop();
          }
        }
      });
    } catch (e) {
      //Do Nothing
    }
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async => false,
      child: Center(
        child: Material(
          borderRadius: BorderRadius.circular(12.0),
          color: Colors.white,
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 26),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(
                    width: 75,
                    height: 75,
                    child: CircularProgressIndicator(
                      value: progress,
                      strokeWidth: 6,
                    )),
                const SizedBox(
                  height: 8,
                ),
                Text(
                  (progress * 100).ceil().toString() + '%',
                  style: const TextStyle(
                    color: Colors.blue,
                    fontSize: 30,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(
                  height: 14,
                ),
                TextButton(
                    onPressed: () {
                      if (progress == 1) {
                      } else {
                        token.cancel();
                      }
                      Navigator.of(context).pop();
                    },
                    style: ButtonStyle(
                      padding: MaterialStateProperty.all<EdgeInsets>(
                          const EdgeInsets.symmetric(
                              vertical: 2, horizontal: 12)),
                      backgroundColor: MaterialStateProperty.all<Color>(
                          Theme.of(context).colorScheme.surface),
                      shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                          RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12.0),
                      )),
                    ),
                    child: Padding(
                        padding: const EdgeInsets.all(8),
                        child: Text(progress == 1 ? "Close" : "Cancel",
                            style: const TextStyle(
                                color: Colors.white, fontSize: 16))))
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/***** Platform Specific(ios, android)****
 * Download directory IOS -> app dir, Android downloads/jumedicine
 * 
 * MediaStore -> Android 
*****/
