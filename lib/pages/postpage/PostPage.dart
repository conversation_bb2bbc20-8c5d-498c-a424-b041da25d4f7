import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'PopCard.dart';
import '../segments/CourseListSegment.dart';

class PostsPage extends StatefulWidget {
  final String siteUrl, categoryName;
  final int categoryId;
  const PostsPage(
      {Key? key,
      required this.categoryId,
      required this.categoryName,
      required this.siteUrl})
      : super(key: key);

  @override
  _PostsPageState createState() => _PostsPageState();
}

List<String> divide(String name) {
  int start = 0;
  start = name.lastIndexOf('(');

  String newName = start == -1 || (start != -1 && name[name.length - 1] != ')')
      ? name
      : name.substring(0, start);
  List<String> res = [
    newName,
    newName.length == name.length
        ? ''
        : name.substring(start + 1, name.length - 1)
  ];
  return res;
}

bool checkIfDownloadable(String subs) {
  return subs.contains('jumedicine.com/');
}

class _PostsPageState extends State<PostsPage> {
  Map<String, dynamic> posts = <String, dynamic>{};
  List<dynamic> categories = <dynamic>[];
  late Future<bool> fetchfuture = fetch();

  Future<bool> fetch() async {
    try {
      Map<String, dynamic> params = <String, dynamic>{};
      params["cat_id"] = widget.categoryId.toString();

      http.Response response = await http.post(
        Uri.parse(widget.siteUrl +
            "/wp-content/themes/lejan_new/mobileApi2017/get_category.php"),
        body: params,
      );
      Map<String, dynamic> jsonData =
          json.decode(response.body) as Map<String, dynamic>;
      if (jsonData['type'] == 'category') {
        categories = jsonData['posts'];
      }
      if (jsonData['file'] == null) {
        jsonData['file'] = [];
      }
      for (int i = 0; i <= jsonData['file'].length - 1; i++) {
        //name first and title second;
        List<String> nt = divide(jsonData['file'][i]["name"]);

        if (posts[nt[1]] == null) {
          posts[nt[1]] = [];
        }
        posts[nt[1]].add(jsonData['file'][i]);
        posts[nt[1]].last["name"] = nt[0];
        posts[nt[1]].last["dname"] = nt[0] + '(' + nt[1] + ')';
        posts[nt[1]].last["id"] = i.toString();

        String subs = posts[nt[1]].last['file'].toString();
        posts[nt[1]].last["secondAction"] =
            checkIfDownloadable(subs) ? "Download" : null;
      }
    } catch (e) {
      return Future.error(e);
    }

    return true;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        title: Text(
          widget.categoryName,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          style: TextStyle(
            color: Colors.white,
          ),
        ),
        shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(28),
                bottomRight: Radius.circular(28))),
        backgroundColor: Theme.of(context).colorScheme.background,
        leading: IconButton(
          iconSize: 30,
          padding: const EdgeInsets.only(left: 10),
          icon: const Icon(Icons.arrow_back),
          color: Colors.white,
          onPressed: () {
            Navigator.pop(context);
          },
        ),
      ),
      body: SafeArea(
        child: FutureBuilder(
          future: fetchfuture,
          builder: (BuildContext context, AsyncSnapshot<bool> snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(child: CircularProgressIndicator());
            } else if (snapshot.hasError) {
              return Center(
                child: TextButton(
                    onPressed: () {
                      setState(() {
                        fetchfuture = fetch();
                      });
                    },
                    style: ButtonStyle(
                      padding: MaterialStateProperty.all<EdgeInsets>(
                          const EdgeInsets.symmetric(
                              vertical: 2, horizontal: 12)),
                      backgroundColor: MaterialStateProperty.all<Color>(
                          Theme.of(context).colorScheme.background),
                      shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                          RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12.0),
                      )),
                    ),
                    child: const Padding(
                        padding: EdgeInsets.all(8),
                        child: Text("Reload",
                            style: TextStyle(
                                color: Colors.white,
                                fontSize: 16.0,
                                fontWeight: FontWeight.w500)))),
              );
            } else {
              return ListView(
                children: [
                  if (categories.isNotEmpty)
                    gridBuilder(
                      categories: categories,
                      site: widget.siteUrl,
                      isInPostActivity: true,
                    ),
                  postsBuilder(posts),
                ],
              );
            }
          },
        ),
      ),
    );
  }

  //enhance by making this reusable component

  Widget postsBuilder(Map<String, dynamic> items) {
    return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 28.0),
        child: Column(children: [
          ...items.keys
              .map((key) => postsListCardTitled(key, items[key]))
              .toList()
        ]));
  }

  Widget postsListCardTitled(String title, List<dynamic> items) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Container(
          padding: const EdgeInsets.only(bottom: 5.0),
          margin: const EdgeInsets.only(bottom: 5.0, top: 20),
          decoration: const BoxDecoration(
              border: Border(
                  bottom: BorderSide(
            color: Colors.black26,
            width: 1.0, // Underline thickness
          ))),
          child: Text(
            title,
            style: const TextStyle(fontSize: 16.0, fontWeight: FontWeight.w700),
            textAlign: TextAlign.left,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        ...items.map((item) => postsListCard(item)).toList(),
      ],
    );
  }

  Widget postsListCard(Map<String, dynamic> item) {
    return GestureDetector(
      onTap: () {
        Navigator.of(context).push(
          HeroDialogRoute(
            builder: (context) => Center(
              child: PostPopupCard(post: item),
            ),
          ),
        );
      },
      child: Hero(
        tag: item["id"],
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 5.0),
          child: Material(
            color: const Color(0xFFDDDDDD),
            borderRadius: BorderRadius.circular(12),
            child: Padding(
              padding:
                  const EdgeInsets.symmetric(vertical: 12.0, horizontal: 10),
              child: Text(
                item["name"],
                style: const TextStyle(
                    fontSize: 14.0, fontWeight: FontWeight.w500),
                textAlign: TextAlign.left,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),
        ),
      ),
    );
  }
}

/***This Class is for Download List*/
class popupCard extends StatelessWidget {
  final Map<String, dynamic> item;
  final VoidCallback update;
  const popupCard({Key? key, required this.item, required this.update})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () async {
        bool? res = await Navigator.of(context).push(
          HeroDialogRoute(
            builder: (context) => Center(
              child: PostPopupCard(post: item),
            ),
          ),
        );
        if (res != null && res) {
          update();
        }
      },
      child: Hero(
        tag: item["id"],
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 5.0),
          child: Material(
            color: const Color(0xFFDDDDDD),
            borderRadius: BorderRadius.circular(12),
            child: Padding(
              padding:
                  const EdgeInsets.symmetric(vertical: 12.0, horizontal: 10),
              child: Text(
                item["name"],
                style: const TextStyle(
                    fontSize: 14.0, fontWeight: FontWeight.w500),
                textAlign: TextAlign.left,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
