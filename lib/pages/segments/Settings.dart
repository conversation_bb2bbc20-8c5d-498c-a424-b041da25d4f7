import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import '../../main.dart';
import 'package:shared_preferences/shared_preferences.dart';
class Settings extends StatefulWidget {
  final String site;
  const Settings({ Key? key, required this.site }) : super(key: key);

  @override
  _SettingsState createState() => _SettingsState();
}

class _SettingsState extends State<Settings> {
  //Variable Futures + Lists + selectedValues
  late Future< dynamic > yearsListFuture = _load('year');
  late Future< dynamic > semestersListFuture = _load('semester');

  dynamic yearValue;
  dynamic finalValue;
  
  
  

  Future< dynamic > _load(String type, {String id = '0'}) async {
    Map<String, dynamic> params = <String, dynamic>{};
    params["cat_id"] = id;

    http.Response response = await http.post(
        Uri.parse(widget.site +"/wp-content/themes/lejan_new/mobileApi2017/get_category.php"),
        body: params,
    );
    Map<String, dynamic> jsonData = json.decode(response.body) as Map<String, dynamic>;
    
    
    List<dynamic> res = jsonData['posts'];
    res.retainWhere((item) => item['postName'].toLowerCase().contains(type));
    if (type.toLowerCase() == 'semester' && res.isEmpty){
      setState(() {
        finalValue = yearValue;
      });
    }
    return res;
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 40, horizontal: 26),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
            "Year & Semester",
            style: TextStyle(
              fontSize: 18.0,
              fontWeight: FontWeight.w700
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 28),
            dropdownWidget(yearsListFuture, yearValue, true),
            if (yearValue != null)
              dropdownWidget(semestersListFuture, finalValue, false),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                button("Reset", true, context),
                if (finalValue != null)
                  ...[
                    const SizedBox(width: 20),
                    button("Save", false, context),
                  ],
              ],
            )
          ],
        ),
      ),
    );
  }




  Widget settingsText(String data, {Color color = Colors.black87}) => Text(
    data,
    style: TextStyle(
      fontWeight: FontWeight.w500,
      fontSize: 16.0,
      color: color
      ),
      textAlign: TextAlign.center,
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
    );




    Widget dropdownWidget(Future future, dynamic selectedValue, bool isYears){
      return FutureBuilder(
        future: future,
        builder: (BuildContext context, AsyncSnapshot snapshot){
          if (snapshot.connectionState == ConnectionState.waiting){
            return const Center(child: CircularProgressIndicator());
          } else {
              if (snapshot.data!.isEmpty) {
                return const SizedBox();
              } else {
                return dropdownContainer(snapshot.data!, selectedValue, isYears);
              }
          }
        }
      );
    }

    Widget dropdownContainer(List<dynamic> data, dynamic dropdownValue, bool isYears) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.symmetric(vertical: 8,horizontal:14),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16.0),
        color: Theme.of(context).colorScheme.surface,
      ),
      child: DropdownButton(
        isDense: true,
        isExpanded: true,
        iconEnabledColor: Colors.white,
        underline: const SizedBox(),
        hint: settingsText("Select A " + (isYears? 'Year':'Semester') , color: Colors.white),
        value: dropdownValue,
        items: data.map((item) {
          return DropdownMenuItem(
            value: item?['postID'],
            child: settingsText(item['postName'], color: Colors.white));
        }).toList(),
        borderRadius: BorderRadius.circular(24.0),
        dropdownColor: Theme.of(context).colorScheme.surface,
        onChanged: (dynamic value){
          if (isYears) {
            setState(() {
              yearValue = value;
              finalValue = null;
              semestersListFuture = _load('semester', id: value.toString());
            });
          } else {
            setState(() {
              finalValue = value;
            });
          }
        }
      ),
    );
  }

  Widget button(String name, bool isReset ,BuildContext context) => TextButton(
    onPressed: (){
      if (isReset){
        SharedPreferences.getInstance().then((pref) async {
          await pref.remove("year");
          await pref.remove("semester_id");
          Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => const EntryApp()));
        });
      } else {
        SharedPreferences.getInstance().then((pref) async {
          await pref.setString("year", yearValue.toString());
          await pref.setString("semester_id", finalValue.toString());
          Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => const EntryApp()));
        });
      }
    },
    style: ButtonStyle(
      padding: MaterialStateProperty.all<EdgeInsets>(EdgeInsets.symmetric(vertical: 2, horizontal: 12)),
      backgroundColor: MaterialStateProperty.all<Color>(Theme.of(context).colorScheme.surface),
      shape: MaterialStateProperty.all<RoundedRectangleBorder>(
        RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.0),
        )
      ),
    ),
    child: Padding(padding: EdgeInsets.all(8) ,child:settingsText(name, color: Colors.white))
  );
}


/* Description
 * futurebuilder for years => if value change => futurebuilder for semester => save or reset
 * if 
 */