import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import '../postpage/PostPage.dart';
import 'package:html_unescape/html_unescape_small.dart';

String decodeHtml(String unescapedText) {
  var unescape = HtmlUnescape();
  var text = unescape.convert(unescapedText);
  return text;
}

class CourseList extends StatefulWidget {
  final String site;
  final String? semester;
  const CourseList({Key? key, required this.site, this.semester})
      : super(key: key);

  @override
  _CourseListState createState() => _CourseListState();
}

class _CourseListState extends State<CourseList> {
  late Future<List<dynamic>> coursesFuture = _loadCourses(context);

  Future<List<dynamic>> _loadCourses(BuildContext context) async {
    List<dynamic> courses = [];
    try {
      String api = widget.site +
          (widget.semester == null
              ? '/wp-content/themes/lejan_new/mobileApi2017/get_cources.php'
              : "/wp-content/themes/lejan_new/mobileApi2017/get_category.php");

      Map<String, dynamic> params = <String, dynamic>{};
      params["cat_id"] = widget.semester;

      http.Response response = await http.post(Uri.parse(api),
          body: widget.semester != null ? params : null);

      Map<String, dynamic> jsonData =
          json.decode(response.body) as Map<String, dynamic>;
      courses = jsonData['posts'];
    } catch (e) {
      return Future.error(e);
    }
    return courses;
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
        future: coursesFuture,
        builder: (BuildContext context, AsyncSnapshot<List<dynamic>> snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          } else if (snapshot.hasError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Text("An error happened while loading",
                      style: TextStyle(
                          color: Colors.black,
                          fontSize: 16.0,
                          fontWeight: FontWeight.w500)),
                  TextButton(
                      onPressed: () {
                        setState(() {
                          coursesFuture = _loadCourses(context);
                        });
                      },
                      style: ButtonStyle(
                        padding: MaterialStateProperty.all<EdgeInsets>(
                            const EdgeInsets.symmetric(
                                vertical: 2, horizontal: 12)),
                        backgroundColor: MaterialStateProperty.all<Color>(
                            Theme.of(context).colorScheme.background),
                        shape:
                            MaterialStateProperty.all<RoundedRectangleBorder>(
                                RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12.0),
                        )),
                      ),
                      child: const Padding(
                          padding: EdgeInsets.all(8),
                          child: Text("Reload",
                              style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 16.0,
                                  fontWeight: FontWeight.w500)))),
                ],
              ),
            );
          } else {
            if (snapshot.data == null) {
              return const Center(
                child: Text(
                  'No Courses',
                  style: TextStyle(fontSize: 18),
                ),
              );
            }
            return gridBuilder(
              categories: snapshot.data!,
              site: widget.site,
            );
          }
        });
  }
}

class gridBuilder extends StatelessWidget {
  final List<dynamic> categories;
  final String site;
  final bool isInPostActivity;
  const gridBuilder(
      {Key? key,
      required this.categories,
      required this.site,
      this.isInPostActivity = false})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    final double screenWidth = MediaQuery.of(context).size.width;

    return Center(
      child: Container(
        alignment: Alignment.topCenter,
        constraints: BoxConstraints(maxWidth: 720),
        child: GridView.count(
          padding: const EdgeInsets.all(16),
          shrinkWrap: true,
          physics: isInPostActivity
              ? NeverScrollableScrollPhysics()
              : null, // to disable GridView's scrolling
          crossAxisCount: screenWidth >= 500 ? 3 : 2,
          childAspectRatio: 1,
          crossAxisSpacing: 10,
          mainAxisSpacing: 14,
          children: categories
              .map(
                (item) => buildCourse(
                    item["postName"], item["icon"], item["postID"], context),
              )
              .toList(),
        ),
      ),
    );
  }

  Widget buildCourse(
      String courseName, String iconURL, int catId, BuildContext context) {
    courseName = decodeHtml(courseName);
    return Padding(
      padding: const EdgeInsets.only(
        top: 15.0,
        bottom: 5.0,
        right: 5.0,
        left: 5.0,
      ),
      child: InkWell(
        onTap: () {
          Navigator.push(
              context,
              MaterialPageRoute(
                  builder: (BuildContext context) => PostsPage(
                      categoryId: catId,
                      categoryName: courseName,
                      siteUrl: site)));
        },
        child: Container(
          padding: const EdgeInsets.only(left: 10, right: 10),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(15.0),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.2),
                spreadRadius: 3.0,
                blurRadius: 5.0,
              ),
            ],
            color: Colors.white,
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 64,
                height: 64,
                decoration: BoxDecoration(
                  image: DecorationImage(
                    image: cacheImage(iconURL),
                    fit: BoxFit.contain,
                  ),
                ),
              ),
              const SizedBox(height: 10.0),
              Text(
                courseName,
                style: TextStyle(
                    color: Theme.of(context).colorScheme.background,
                    fontSize: 14.0,
                    fontWeight: FontWeight.w500),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  ImageProvider<Object> cacheImage(String iconURL) {
    const List<String> iconsList = [
      '1.png',
      '10.png',
      '11.png',
      '12.png',
      '13.png',
      '14.png',
      '15.png',
      '16.png',
      '17.png',
      '18.png',
      '19.png',
      '2.png',
      '20.png',
      '21.png',
      '22.png',
      '23.png',
      '24.png',
      '25.png',
      '26.png',
      '27.png',
      '28.png',
      '29.png',
      '3.png',
      '30.png',
      '31.png',
      '32.png',
      '4.png',
      '5.png',
      '6.png',
      '7.png',
      '8.png',
      '9.png',
      'Behavioural-01.png',
      'Biostat-01.png',
      'Cns-01.png',
      'Endocrine.png',
      'GI-01.png',
      'Genetics-01.png',
      'Global health-01.png',
      'Gyne-01.png',
      'Histology-01.png',
      'Introductory-01.png',
      'MSS-01.png',
      'Organic-01.png',
      'Pediatrics-01.png',
      'Physiology-01.png',
      'Surgery-01.png',
      'Urogenital-01.png',
      'anatomy-01.png',
      'biolab-01.png',
      'biology-01.png',
      'book.png',
      'category_screens.PNG',
      'category_screens_old.PNG',
      'chemistry-01.png',
      'community-01.png',
      'cs-01.png',
      'gardiology-01.png',
      'hemetology-01.png',
      'immunology-01.png',
      'medicine-01.png',
      'microbiology-01.png',
      'pathology-01.png',
      'pharmacology-01.png',
      'physics-01.png',
      're-design respiratory-01.png',
      'respiratory-01.png'
    ];

    String iconName = iconURL.split('/').last;
    if (iconsList.contains(iconName)) {
      return Image.asset('assets/icons/' + iconName).image;
    } else {
      return Image.network(iconURL).image;
    }
  }
}
