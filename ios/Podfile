cat > ios/Podfile <<'RUBY'
platform :ios, '13.0'

use_frameworks! :linkage => :static
use_modular_headers!

ENV['COCOAPODS_DISABLE_STATS'] = 'true'

project 'Runner', {
  'Debug' => :debug,
  'Profile' => :release,
  'Release' => :release,
}

def flutter_root
  generated_xcode_build_settings_path = File.expand_path(File.join('..', 'Flutter', 'Generated.xcconfig'), __FILE__)
  unless File.exist?(generated_xcode_build_settings_path)
    raise 'Generated.xcconfig must exist. If you are running pod install manually, run "flutter pub get" first.'
  end
  File.foreach(generated_xcode_build_settings_path) do |line|
    matches = line.match(/FLUTTER_ROOT\=(.*)/)
    return matches[1].strip if matches
  end
  raise 'FLUTTER_ROOT not found in Generated.xcconfig'
end

require File.expand_path(File.join(flutter_root, 'packages', 'flutter_tools', 'bin', 'podhelper'), __FILE__)

flutter_ios_podfile_setup

target 'Runner' do
  flutter_install_all_ios_pods File.dirname(File.realpath(__FILE__))

  # Do NOT add "pod 'Firebase'".
  # Make Firebase subspecs modular so they can be imported with @import.
  pod 'FirebaseCore',          :modular_headers => true
  pod 'FirebaseMessaging',     :modular_headers => true
  pod 'FirebaseInstallations', :modular_headers => true
  pod 'GoogleUtilities',       :modular_headers => true
  pod 'PromisesObjC',          :modular_headers => true
end

post_install do |installer|
  installer.pods_project.targets.each do |t|
    flutter_additional_ios_build_settings(t)
    t.build_configurations.each do |config|
      config.build_settings['DEFINES_MODULE'] = 'YES'
      config.build_settings['CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES'] = 'YES'
      config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '13.0'
    end
  end
end
RUBY
