{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ffa929e6b9662e017a39ed26bde94789", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "MODULEMAP_FILE": "Headers/Public/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2"}, "guid": "bfdfe7dc352907fc980b868725387e98ccf74204e921ae82d0e2ae8f6511c394", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9885541c7ac873c0be506f70196e38bdf7", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "MODULEMAP_FILE": "Headers/Public/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "OTHER_LDFLAGS": "", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES"}, "guid": "bfdfe7dc352907fc980b868725387e98fa2a91bcb175732c11e7bb18f9035753", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9885541c7ac873c0be506f70196e38bdf7", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "MODULEMAP_FILE": "Headers/Public/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "OTHER_LDFLAGS": "", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES"}, "guid": "bfdfe7dc352907fc980b868725387e9832b4609c8b3580ccc0435cc30d9955b1", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984f1490b469fba27c8bda75d88033b5b0", "guid": "bfdfe7dc352907fc980b868725387e98e48fe5ec6edf90e71a43dd7211c81184"}], "guid": "bfdfe7dc352907fc980b868725387e9819093972d3b8b3fc84cccb483eb3d37b", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9859041c989398541968862408c13ff188", "guid": "bfdfe7dc352907fc980b868725387e98b3d85a05c4c14067815e295f31dab067"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985712a0847c65b4ef658b18f34b581763", "guid": "bfdfe7dc352907fc980b868725387e9852d28dc706a223a512405240a07ad283"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821d91442938533897861f3fdf58d76a0", "guid": "bfdfe7dc352907fc980b868725387e98378eac95d491548577dce1b31ad3098d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db94b02cf31f383f873f18c6227a0f5f", "guid": "bfdfe7dc352907fc980b868725387e983fb9617748f8c819d4d44699162a2f27"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988654b115945fd26072c9ba35812049d8", "guid": "bfdfe7dc352907fc980b868725387e98ec5b75ad037e8f719473d6b1a37acf17"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863a03fbfbf0f2602ba03df5b94cbae32", "guid": "bfdfe7dc352907fc980b868725387e98f7341bcc03a2bae2afa74fd610c39818"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852a322a21649f8a075cf5727a29499a9", "guid": "bfdfe7dc352907fc980b868725387e98bf4fdb3db5b64aba443536ae05577fed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0cd57902d005ff4227ed745a4a6d2d4", "guid": "bfdfe7dc352907fc980b868725387e982689c1c341af6981f4a841276d5ece1c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807a3af3d37825380774205652f296e32", "guid": "bfdfe7dc352907fc980b868725387e98738a62627c128237be5c65d17d1538f9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b817cb7c6b102a6176712c95ff0002e", "guid": "bfdfe7dc352907fc980b868725387e98d09c8224d091342f3ce90911ff7dc8f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca1cdf25dfdc97d3647a2fd80eed050a", "guid": "bfdfe7dc352907fc980b868725387e986407810d3b36f80bd30c59cf9b4f4dba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c016a97a011dd917bc4a91135718c03", "guid": "bfdfe7dc352907fc980b868725387e98d33c2b84179d682d2e4188651a9628fb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835761100d6eb5aa86e65314aad2cf6ff", "guid": "bfdfe7dc352907fc980b868725387e986595b90faa14f93d9d59d6643eedc3af"}], "guid": "bfdfe7dc352907fc980b868725387e98175c79f84f89b80a64a2e4d098a1e65a", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98999e45d6fbe1eae3bf29d889d2c5aa79", "type": "com.apple.buildphase.frameworks"}, {"alwaysOutOfDate": "false", "alwaysRunForInstallHdrs": "false", "buildFiles": [], "emitEnvironment": "true", "guid": "bfdfe7dc352907fc980b868725387e98718d969a8396107c146b6d6b61a93de5", "inputFileListPaths": [], "inputFilePaths": ["${DERIVED_SOURCES_DIR}/${PRODUCT_MODULE_NAME}-Swift.h", "${PODS_ROOT}/Headers/Public/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "${PODS_ROOT}/Headers/Public/FirebaseCoreInternal/FirebaseCoreInternal-umbrella.h"], "name": "Copy generated compatibility header", "originalObjectID": "C433044440D01709B09F5A30AE243B74", "outputFileListPaths": [], "outputFilePaths": ["${BUILT_PRODUCTS_DIR}/${PRODUCT_MODULE_NAME}.modulemap", "${BUILT_PRODUCTS_DIR}/FirebaseCoreInternal-umbrella.h", "${BUILT_PRODUCTS_DIR}/Swift Compatibility Header/${PRODUCT_MODULE_NAME}-Swift.h"], "sandboxingOverride": "basedOnBuildSetting", "scriptContents": "COMPATIBILITY_HEADER_PATH=\"${BUILT_PRODUCTS_DIR}/Swift Compatibility Header/${PRODUCT_MODULE_NAME}-Swift.h\"\nMODULE_MAP_PATH=\"${BUILT_PRODUCTS_DIR}/${PRODUCT_MODULE_NAME}.modulemap\"\n\nditto \"${DERIVED_SOURCES_DIR}/${PRODUCT_MODULE_NAME}-Swift.h\" \"${COMPATIBILITY_HEADER_PATH}\"\nditto \"${PODS_ROOT}/Headers/Public/FirebaseCoreInternal/FirebaseCoreInternal.modulemap\" \"${MODULE_MAP_PATH}\"\nditto \"${PODS_ROOT}/Headers/Public/FirebaseCoreInternal/FirebaseCoreInternal-umbrella.h\" \"${BUILT_PRODUCTS_DIR}\"\nprintf \"\\n\\nmodule ${PRODUCT_MODULE_NAME}.Swift {\\n  header \\\"${COMPATIBILITY_HEADER_PATH}\\\"\\n  requires objc\\n}\\n\" >> \"${MODULE_MAP_PATH}\"\n", "shellPath": "/bin/sh", "type": "com.apple.buildphase.shell-script"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "libFirebaseCoreInternal.a", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.library.static", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}