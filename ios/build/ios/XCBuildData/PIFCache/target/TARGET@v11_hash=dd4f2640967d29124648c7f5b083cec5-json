{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980b5e4fb9f1b8cc22b910bb5df7a50e43", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FMDB/FMDB-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FMDB/FMDB-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FMDB/FMDB.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FMDB", "PRODUCT_NAME": "FMDB", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c8b7a9fb5a7d4be60b4be57c2edf6ba6", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d5c85303da78470079e76a79a2f23185", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FMDB/FMDB-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FMDB/FMDB-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FMDB/FMDB.modulemap", "PRODUCT_MODULE_NAME": "FMDB", "PRODUCT_NAME": "FMDB", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989eea7d3ee4cf254117682ee36f6853cb", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d5c85303da78470079e76a79a2f23185", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FMDB/FMDB-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FMDB/FMDB-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FMDB/FMDB.modulemap", "PRODUCT_MODULE_NAME": "FMDB", "PRODUCT_NAME": "FMDB", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e27ad195513e66b1a4151138e628e167", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98585e9487e82a1a808271d7d2672fced4", "guid": "bfdfe7dc352907fc980b868725387e9865fda49bdf1dcfa4e9da26ceb8165bd4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803cb2c0b2a879e8ebc25a8c6a28c111f", "guid": "bfdfe7dc352907fc980b868725387e98d20ad6972ddba643b1de6337ed9e1f7e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98030ba291d55107a48c0f89b638b2f637", "guid": "bfdfe7dc352907fc980b868725387e98c2b68184cd35f122410d186265b980ad", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98618173c68f673aada324847ac1baf2b9", "guid": "bfdfe7dc352907fc980b868725387e98efca2ce05f51fd6be386deb70052f223", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989bf9250381db270bc6cd820034bd216f", "guid": "bfdfe7dc352907fc980b868725387e982626799b1333e45c1f998331bf381382", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859e350ef85c1087d75dbbc85d1949d97", "guid": "bfdfe7dc352907fc980b868725387e9856128025acc78956afe3088802c254e8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc8d1dc930d2eed73a48f464269743f3", "guid": "bfdfe7dc352907fc980b868725387e98bf6fae3a591a6f864d5307ffa3570cb3", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9884058f59e7227408554da08a6932b5b3", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98aa142d500b1d946ce27e27b2cb6362de", "guid": "bfdfe7dc352907fc980b868725387e9873ca6e9e1440aa07e463192bb0a1ffd8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856ee4b8b5316d6675652e3a15b1b116b", "guid": "bfdfe7dc352907fc980b868725387e980535748d78847508675e078433c4f669"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827b25f2e2b6cff2d65ed02ec80ab9908", "guid": "bfdfe7dc352907fc980b868725387e98f4863a505748b22aaa710e87124f07cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa0fdf30f95f632feaa0aadfc1a4cbb3", "guid": "bfdfe7dc352907fc980b868725387e98bb3d3d7d0ed05f2532b64e193475371a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d54ec51cb3dcc0563f0bfb5b8f7a393", "guid": "bfdfe7dc352907fc980b868725387e98805873256ed450c2a2ddf764a5f66369"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818a50f1fea1e1dcea61f8af7056d5396", "guid": "bfdfe7dc352907fc980b868725387e98f5da93ea763ba8cc3e4d861507342381"}], "guid": "bfdfe7dc352907fc980b868725387e98b9bc9324f90c2dab03adee919396678f", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9834dfa23abd1ee3cbdf63f2e7e7c8692e", "guid": "bfdfe7dc352907fc980b868725387e9805902f56216b1706a8aabbabc4bba50f"}], "guid": "bfdfe7dc352907fc980b868725387e98071ff318d59a6abed859980fd2e05632", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e988d3e5636b683337cacda1e0e0c428b0a", "targetReference": "bfdfe7dc352907fc980b868725387e98cab8b41b13f524e82b01b79420264253"}], "guid": "bfdfe7dc352907fc980b868725387e98085578d61aa97f83401f998e64ed1cc8", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98cab8b41b13f524e82b01b79420264253", "name": "FMDB-FMDB_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98e5199a3f70555689be7018ee7f9f878d", "name": "FMDB", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983a0b2834dab2d28f0f87c3e6cdedcc0d", "name": "FMDB.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}