{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d3e34fb64a93c59b7a4db0ea5a594d0a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98bab9ffcd1ba028179a141793ace0e3b3", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9888871e04866b2a4980312ebe720ba0ed", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985b1e0ce2697f584b7c1dc9970e4aa271", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9888871e04866b2a4980312ebe720ba0ed", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98eb62feba4fddbaa2bcca285a928fb9a0", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983580a700d5ee76060378671a0bcd520b", "guid": "bfdfe7dc352907fc980b868725387e98c886f583d5e4dada88dc59db3efa6b06"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982594868831671ef61897a468c55a23ae", "guid": "bfdfe7dc352907fc980b868725387e98570340a5574a4defd73e50eb8a761863", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b332a440ac00353750f3c6dce2bbda7b", "guid": "bfdfe7dc352907fc980b868725387e98d096eaa880a3bd7b8f69ed39829d9c3e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba05e7f28b81c8e19868530bdb620e2e", "guid": "bfdfe7dc352907fc980b868725387e98c4c2a05cbea8e59b972e7e063637c209"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1c6d0b6556b868d79f3ccb04f998123", "guid": "bfdfe7dc352907fc980b868725387e987956cf46c4ab5bb4ef64f379a2638ab7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842781a343ac9cede4e253745c205cc64", "guid": "bfdfe7dc352907fc980b868725387e98ee341a88f0068c5c1930de7db45cceb0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c7d6cbcf4f9100d887074c2d0c1a01f", "guid": "bfdfe7dc352907fc980b868725387e98b7627f4e703c70a35479d2cbe81deffe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee10ca25b6a2a80077353f107a5a6519", "guid": "bfdfe7dc352907fc980b868725387e989c890c2b038b9d7452fcd88ee834dd64"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98495661753319750d3cd320d0f2f79a66", "guid": "bfdfe7dc352907fc980b868725387e98d4f060f7044cbaba71417a35aac01022", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c770a3b6e0570d978fca2752e9a1d12", "guid": "bfdfe7dc352907fc980b868725387e987470e3bad69dca3dcbd601efbe5db488"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a9de046fdfa080cb43f484d175f1aa4", "guid": "bfdfe7dc352907fc980b868725387e988b385a9d772ff457bacb707740116af6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cacb513140c4634828c080132c9fb60b", "guid": "bfdfe7dc352907fc980b868725387e987de5a41fe6c465ad6711e1e8a9753df2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985513068520848b8b8d52cadba083a94e", "guid": "bfdfe7dc352907fc980b868725387e98cc1786a14dce92f88d36ec73888b27c1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989af3263cd7ed947f6433f820a5ac062c", "guid": "bfdfe7dc352907fc980b868725387e98f4d021dce2084e662e68ed1a4c579a06"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986685671474c9f56b49ef530d940c0df8", "guid": "bfdfe7dc352907fc980b868725387e983b702af01f7fc4b3dcfd07937fd04adc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98290f069b65548c3ed7f5afa683274e91", "guid": "bfdfe7dc352907fc980b868725387e98b70d42bda48f6e37fb0edf76b080ee91"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855ede7931cecfe5c9ca04caece34276b", "guid": "bfdfe7dc352907fc980b868725387e98bbcd12796ba4503c56d3f2c9d801fc2d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986df71760fa4576a1f99b5687fbac8aa7", "guid": "bfdfe7dc352907fc980b868725387e987c73574fd654b8017add4fb3809c0ccb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d98bf827329e62d2b17c7dad5952760c", "guid": "bfdfe7dc352907fc980b868725387e9880029a7f46682bad5c2c395a140ce2e1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f22a056eb04dac01b350afb4aab4fcf1", "guid": "bfdfe7dc352907fc980b868725387e986c332b60189209f9951c40b44bbf0e8a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982da092b163f7fd99bd35872ec64a210a", "guid": "bfdfe7dc352907fc980b868725387e98ee55dd4dcac6039d453285176213f7e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840a07ea44037cdc878c1592ffe588bb8", "guid": "bfdfe7dc352907fc980b868725387e9875f8cece878aa396046e50c066709f11", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986c72da260a8b5c89dc1471a1bd7dd4ed", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982062e397a65f86dad40f6e803317560a", "guid": "bfdfe7dc352907fc980b868725387e98c440347fd87f628099ab0942e98e5338"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f17f84ae73df1b0dec994ba4de045118", "guid": "bfdfe7dc352907fc980b868725387e98eb69b99857d2121b9713232a0e5d8a2f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b0800d38623adab172d676d3387a7ac", "guid": "bfdfe7dc352907fc980b868725387e988f2c2d405f3dbb6ad8f16158d21824b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98740aa149026d7901a1b4f28ed41093b6", "guid": "bfdfe7dc352907fc980b868725387e98be9c7b60013198459ac1eca04418cb36"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ef02f1708f413946cd2ca28179ad435", "guid": "bfdfe7dc352907fc980b868725387e98406cce95e2d91ff922939d3c22ddefa2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989dac097fb8df628b2febc22304b3000a", "guid": "bfdfe7dc352907fc980b868725387e98195fc70346fddc0d2a74ae6f7894ca8e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826865540b548405c542ad3ebb7b3b49c", "guid": "bfdfe7dc352907fc980b868725387e986635d65daea9ef9d4156e13d021aa7a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f47bc5c370acb535afc794886ff233f", "guid": "bfdfe7dc352907fc980b868725387e98901e3fec13d1acb54e26f3e5a18a8ee7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4f7fe91ca718b9dd1caeaa74810ee1d", "guid": "bfdfe7dc352907fc980b868725387e98057d3df35c4d750aba132a182135b966"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981fe24bb76de3d936bc4b523d1f445c1c", "guid": "bfdfe7dc352907fc980b868725387e984b1b3d3ce401c2b0a4bbadc59a1f5b65"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d77585cd35949172a169f845e3df0668", "guid": "bfdfe7dc352907fc980b868725387e9811cb59b0c1c65f50b137789644c6cab2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad207954f0937fe7667e4cf702280261", "guid": "bfdfe7dc352907fc980b868725387e988b831a7f91cccd5c1cc7bf7926ffd9fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b497572ee831f97b9619e23713585eb", "guid": "bfdfe7dc352907fc980b868725387e98541c4a6468a0070e51f0b13a761480c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812bc55ea69aac3be6ad33f714f45cabc", "guid": "bfdfe7dc352907fc980b868725387e9839b7388e1a98314391ea2fb30e0e5b02"}], "guid": "bfdfe7dc352907fc980b868725387e9883dd244eee3306a92c06f8fe2944f6b2", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9834dfa23abd1ee3cbdf63f2e7e7c8692e", "guid": "bfdfe7dc352907fc980b868725387e98058a2929183caa5fe6e5655f1312db16"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5192e7597d49b2508d02a0ca39a2058", "guid": "bfdfe7dc352907fc980b868725387e983aaf6c0e3349a5a8aaedc89185bb8a8f"}], "guid": "bfdfe7dc352907fc980b868725387e98227255fddfed540f4f332e651de6d140", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98999b1f8e26aa343e49f0189a5ad19ca7", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}