{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d1f172d21ae842ca019562b65967198e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2"}, "guid": "bfdfe7dc352907fc980b868725387e98d626ab17fc40df4008f6935e7a4af44d", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9878e5a92662038b3b174d929680863d89", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "OTHER_LDFLAGS": "", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES"}, "guid": "bfdfe7dc352907fc980b868725387e98b8a6db484633c35b7baeb86db4e72fd0", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9878e5a92662038b3b174d929680863d89", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "OTHER_LDFLAGS": "", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES"}, "guid": "bfdfe7dc352907fc980b868725387e985a1621c668304624df2ce905ec7abef9", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98010cbf6d839175954a5eeff4d89dd2f2", "guid": "bfdfe7dc352907fc980b868725387e9886e3543e05e543dcf9f46a9a38867736"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98148f5406fb23750a9c4672069e9b465f", "guid": "bfdfe7dc352907fc980b868725387e98b92098efded3e1980a1ee81088881e0a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f79b40e7bdd5838778d8443935b9078e", "guid": "bfdfe7dc352907fc980b868725387e98779811c348a2a5a1e714a58f76832c39"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982bd5143c32fd23969f564a627b7c370a", "guid": "bfdfe7dc352907fc980b868725387e982ab51551b40dbe18dd7bd09759d322fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859364895d4e04c12de962bd2aece0d13", "guid": "bfdfe7dc352907fc980b868725387e98318022cdd63799159ec3335026b49317"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854a16c677b152d9b0d37ea8559490054", "guid": "bfdfe7dc352907fc980b868725387e986e70080721d9409be18fe43ad76bbc52"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803785900ca5bad809bdacde0501e1f9b", "guid": "bfdfe7dc352907fc980b868725387e985de26eefff88f0f1f812a69d38a7d154"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f3e4f06e52077732f36dcbda9752149d", "guid": "bfdfe7dc352907fc980b868725387e9841dfdbc0ae7915a027c0ccea1d090b6c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4ef97d577fde50eca9f8e184c4e3161", "guid": "bfdfe7dc352907fc980b868725387e981f75ab817c10cf5b42450ddc763083d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848d4c376f251dca605918e38a39026b8", "guid": "bfdfe7dc352907fc980b868725387e98daa4479838d9b28e7cd0d9ec3d2020c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f5a42a5003b914416d01254c31f75ed", "guid": "bfdfe7dc352907fc980b868725387e98d868917258b325363abaee6684dd8920"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4e1a442e64d77ff3a93c8eaec67ec9d", "guid": "bfdfe7dc352907fc980b868725387e98eaa27e344faadcfe7400267ffb125fb7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5ce0a9a5438360eaaafe725e0b5c681", "guid": "bfdfe7dc352907fc980b868725387e98aa876233fa1ddef156b665841923fd0e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836aa31b4e95337a411faa6b923021063", "guid": "bfdfe7dc352907fc980b868725387e98777e3ef7b31a2439c26783ca052f0e85"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ccfabcaec9b24fe7831f898c6acfef61", "guid": "bfdfe7dc352907fc980b868725387e9879f6889762912e15be8e3fc8235bf95d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e1cc314c3a7cbeb6efb3d74a91fbc62", "guid": "bfdfe7dc352907fc980b868725387e9884434f197a90242a814d175dc76ea0a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ec3256470521481644507a38629ea4a", "guid": "bfdfe7dc352907fc980b868725387e98552772391efc5ef58379bdf60f689f3b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987670c11aa9506032f7391ca1878199df", "guid": "bfdfe7dc352907fc980b868725387e98088f10bed27d7637e7fbd1a4d4f7c1a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e5e29d743eef4cde58bd077b50ed49d", "guid": "bfdfe7dc352907fc980b868725387e984169b7972ceb4405116af20fc05dab24"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e96ed27c7fcd1337e227369f3ed45dc7", "guid": "bfdfe7dc352907fc980b868725387e98b9b920d8a46dbf53f6f7bdee1c5d1e54"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac81981dda83817929b72392b7407e91", "guid": "bfdfe7dc352907fc980b868725387e98c7d0e5456101d10a40a4aa0b9a0e4df2"}], "guid": "bfdfe7dc352907fc980b868725387e98163e3b3a99ef19c6154eb48ee777d1e1", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f171fa5009acd14a677fec0a0a79de74", "guid": "bfdfe7dc352907fc980b868725387e9821e8b65fd591aa8ceb1cb1667124d177"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6c0437033d4c436bb0fb39dcbf13254", "guid": "bfdfe7dc352907fc980b868725387e980e0379dbc0de384097a1b91b85ccad17"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ad7c4bf4eeec923d8691ed9b481bacf", "guid": "bfdfe7dc352907fc980b868725387e98d4699c018b7e893aa9b7346d51727ec2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98061b18e4eec05ed2e120985fb7f868e1", "guid": "bfdfe7dc352907fc980b868725387e9816ec63052be829ca85cb59c2a300c853"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98239c2c426f2d8ea8868dbb8764886ef0", "guid": "bfdfe7dc352907fc980b868725387e983225b9c1d53a82ddc3da875bcb92410a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fcbc39734973465d17d072bc2019988b", "guid": "bfdfe7dc352907fc980b868725387e98a090f299c931e9dd23fa7a3704bcd743"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880d99bf6f8f298a1b0eaa99808263f2d", "guid": "bfdfe7dc352907fc980b868725387e9809e15cc5d0c3c82c45a97aa77f5f67f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876937ac22023d0689a1602f40fe1d462", "guid": "bfdfe7dc352907fc980b868725387e989f9af45120933599d6a3b5e73c038734"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c18e571e35f1a5283d7eac598fe37d3b", "guid": "bfdfe7dc352907fc980b868725387e983cc9f03a9fdb479d7cb72e360fe5d166"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d342cfaf11a59e2be6899567a63779c0", "guid": "bfdfe7dc352907fc980b868725387e9889ce6832e0a6ace03803c0415d24cf0b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7f0d89ef8df820e8dd2d6835d32661a", "guid": "bfdfe7dc352907fc980b868725387e98cc38f0e75d8884b221e1d3abe5db78e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb9d60cacd71a47fc90fe8f84c005d5b", "guid": "bfdfe7dc352907fc980b868725387e986b38c79a5fd27a9ced1c44bd218c2994"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bfe774c7ae91a39bbb7f8a5543cbef83", "guid": "bfdfe7dc352907fc980b868725387e9856344e6f59346419d0cddc9e2d8ae7a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836a9ba28120c42fb6d76f75152e41152", "guid": "bfdfe7dc352907fc980b868725387e983866a39911b068932d08fcdcf83ac038"}], "guid": "bfdfe7dc352907fc980b868725387e98a11ae1aaf1ba1c949956d8c23a0f9c0a", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e983f92bc9d3e5112487562f9859ffcf7be", "type": "com.apple.buildphase.frameworks"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "libFirebaseCore.a", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.library.static", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}