{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980b2e316c2f28979cc31c39aaf085cb42", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "MODULEMAP_FILE": "Headers/Public/FBLPromises/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "PromisesObjC", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2"}, "guid": "bfdfe7dc352907fc980b868725387e98a6fe2e682d87467f802d6bbf0419f341", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fc4396882eb166a6ed394d81d705ddf4", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "MODULEMAP_FILE": "Headers/Public/FBLPromises/PromisesObjC.modulemap", "OTHER_LDFLAGS": "", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "PromisesObjC", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES"}, "guid": "bfdfe7dc352907fc980b868725387e980c110f7bd15596bc7909853a44102a10", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fc4396882eb166a6ed394d81d705ddf4", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "MODULEMAP_FILE": "Headers/Public/FBLPromises/PromisesObjC.modulemap", "OTHER_LDFLAGS": "", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "PromisesObjC", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES"}, "guid": "bfdfe7dc352907fc980b868725387e985b2b6150ab0c078e929e12e622296eab", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987e3c4ea52f0ab3e7b69d49c243e294f4", "guid": "bfdfe7dc352907fc980b868725387e9876957c3a75131de39ff2a8765217a406"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987cc5be1862a23bb4220049f40049c25f", "guid": "bfdfe7dc352907fc980b868725387e98342e6dee6c4d027601da70d28a5cd532"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae5757fb0039e9647ae223b4b214438a", "guid": "bfdfe7dc352907fc980b868725387e985e9fb9794117d16a7e9ea8616bc450a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870e31c224ee5bbf5601f7470d8c9ede4", "guid": "bfdfe7dc352907fc980b868725387e9830a9ec669fb7641d4566493dd630892a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c4c747f82a138deaa6af0739a9e0876", "guid": "bfdfe7dc352907fc980b868725387e98cef8d74d13e7d6f6684269ff8ffdae6c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e6aced6b0e1ec488594700039fcfd08", "guid": "bfdfe7dc352907fc980b868725387e98cd53b16b12f000e42805604fd31b2ba4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cda776e71bd6d3854db3c18bae504e34", "guid": "bfdfe7dc352907fc980b868725387e9813e7a94d82d5dcb2a13fe1d66c637094"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889ccadee52eb9b5c6a97c74c77180d3e", "guid": "bfdfe7dc352907fc980b868725387e9818b6f5f62bc583e646ed9252d5edf93c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98228cc1f0b8d61a41ec56d3b232f6dc2b", "guid": "bfdfe7dc352907fc980b868725387e982d7d9876f3f7a213a2e55dbaf0a43f14"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986537b37ad009db5261711976d1a1e0fa", "guid": "bfdfe7dc352907fc980b868725387e98fa751a3ab9854a53bb00e55acab650cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989dc3a4e43a1ac7884259d9f962139c68", "guid": "bfdfe7dc352907fc980b868725387e980578dd33c52fe932d4e3d04a914d2196"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98356715c0b3de05e5e6154733cd13eddd", "guid": "bfdfe7dc352907fc980b868725387e98e91956f0351dd88bba3e7c3b08700ed0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826c030bb318281fb451dc7cfd535b1cc", "guid": "bfdfe7dc352907fc980b868725387e98b3dade91651ddb04c2702464ef3be701"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be20a7f804efcad85792ef8dcfcc3d90", "guid": "bfdfe7dc352907fc980b868725387e9847fdce71337b20e0327a9b9572b23fae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d063bc7b4a1cac0f1748e673376feeca", "guid": "bfdfe7dc352907fc980b868725387e985fb84c3a6fdc45ebbbc291ad40bee460"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98828d6bdfdf3b14448e1fc9c7f31e2df4", "guid": "bfdfe7dc352907fc980b868725387e98179781c0514446ecf608571cea907a0a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c81457557322908d8f4e7fa77a5fb5e5", "guid": "bfdfe7dc352907fc980b868725387e9891be8ee649ec228355ce0ce44d8963dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98627efcca2044cc8e40ccc354db4095f4", "guid": "bfdfe7dc352907fc980b868725387e98cb702c4be905d6e812b08ad09cbd1ae5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ac38292efab5ef7ba2ee838650a50c7e", "guid": "bfdfe7dc352907fc980b868725387e98195352b6a35d3db9076022acf0f82ee7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856a5d28c43531d90e66eaea6112928d0", "guid": "bfdfe7dc352907fc980b868725387e989fea782864e75545c029aa5d1a701a78"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df7cdba7b48f8cb5bb0137123bdb77c5", "guid": "bfdfe7dc352907fc980b868725387e981566cf1f2977da61886264cf09f85a4f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b268931c1ca70d31c75b8b9002fcd07", "guid": "bfdfe7dc352907fc980b868725387e989bebfee039d98dbc4576c947bb0b8f85"}], "guid": "bfdfe7dc352907fc980b868725387e98787eff2a231c0d59c6d5a4d3cfa98a61", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98897e24dbacd6d17b55b11faef55b287e", "guid": "bfdfe7dc352907fc980b868725387e98f1b590320bc4410632c2c0fefb0b3014"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896e12d93e56d11a7531c5d2887701a08", "guid": "bfdfe7dc352907fc980b868725387e9862c75b7581e641c6a09e10b73244d28b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983002e8c3f53ee98a7c5e50c2c68beaf6", "guid": "bfdfe7dc352907fc980b868725387e9837767f0afef324e659b484d5416a7ec7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9b6b8205cb6a405c0cd290960db3884", "guid": "bfdfe7dc352907fc980b868725387e98d2ee40a9d4a6bfc666b6bf9456e0fae9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98455765f1a56b647ca4b3ebf81e675aa8", "guid": "bfdfe7dc352907fc980b868725387e98f48738dbf3560f65a4f5ec14a35fc309"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986509ba35010f5ad22e8c5f26ddd3c606", "guid": "bfdfe7dc352907fc980b868725387e984577618048face906953fb57dedd6929"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98555ca2fae4058b9e548b5c3842861b61", "guid": "bfdfe7dc352907fc980b868725387e98424d28b79561da313b732070fa57120f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d7f8ee884b71d8a7e4b7ba6583bcbbe", "guid": "bfdfe7dc352907fc980b868725387e9870b4f0991c60771e41a8fe5f1d1fd78a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e66bc90fe05f27f8b835abfa654792a0", "guid": "bfdfe7dc352907fc980b868725387e98a7e1e0b8a303d896c9eba64aec40f5e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b70273a4d270d6a0d9d1fee903611599", "guid": "bfdfe7dc352907fc980b868725387e98030f60b89659a527003045ef430bdf1a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ee6bf4f260b4d8b01c58c68c77bb655", "guid": "bfdfe7dc352907fc980b868725387e98db32eede745a9ed6c11a4e483c28fb6b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984849248a363245ad4301823629cd8f1a", "guid": "bfdfe7dc352907fc980b868725387e989d80388f1a16d09670852e7ab7e72304"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b07f60a527c34ab7361269fb2b5cd51f", "guid": "bfdfe7dc352907fc980b868725387e98506a2b9ee42bda48bf2ec66acf01fa66"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982817edd252875e2d05b96ace2773119b", "guid": "bfdfe7dc352907fc980b868725387e982008b99f9b0ef2910917d1501f3ab746"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98149b3e2b38dbb916706113dc2d95804e", "guid": "bfdfe7dc352907fc980b868725387e9873dc541deeb4510d1746d80b196a9ae9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e65109cad37fda3924e6bb5c1af0843", "guid": "bfdfe7dc352907fc980b868725387e9860c3dbd5ed7f2c386a33fbabbfd3feb2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853573ccb8584584ba93679ad690fdd10", "guid": "bfdfe7dc352907fc980b868725387e98258db2a4c20d6f4ec4da2554d460baf0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800cff5a513157a630e2ed185692064fa", "guid": "bfdfe7dc352907fc980b868725387e98abb9152c92d831cd62577abc45b0e8cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983973b10b20c08fffd9d3782bc8bdbc05", "guid": "bfdfe7dc352907fc980b868725387e985ca4fec62b9f38c45b3dd756ebf4bc98"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc0d6370cc9f124ac21562d7df1d66be", "guid": "bfdfe7dc352907fc980b868725387e98415888cfec748ed23699c50abc532879"}], "guid": "bfdfe7dc352907fc980b868725387e984d41c9292155af173e0bcaaa8f2eb571", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98036221cb2befee77307c24ba9bda2858", "type": "com.apple.buildphase.frameworks"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "libPromisesObjC.a", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.library.static", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}