{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982e2fddb16ba54724ce4cc8f266677a4f", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986f60ad41630d9e7ebc6257f2b7c9771a", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b2c7366ebc2188f55b8bd35ec82bfb44", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9808ebb61cc9b6bf2730a4627e98ee10ff", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b2c7366ebc2188f55b8bd35ec82bfb44", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984072357f32a9f8fc95b3c02424bde0a8", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984bf85c9836427fa1fedaec025cec58e9", "guid": "bfdfe7dc352907fc980b868725387e981ff4a9559e460669a68840f764c2eea4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7db556d946431366bdc0f54260be6c3", "guid": "bfdfe7dc352907fc980b868725387e983e067ebccc664fa706fbc800b29a72d7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985970d825e22a43d52b6cb2dba39ceb62", "guid": "bfdfe7dc352907fc980b868725387e98b38df8bd91929c43fd7cfd3017c0f69d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836f3e2e1bdf08c63408b974a9cf9e850", "guid": "bfdfe7dc352907fc980b868725387e980ae65292eabbf781bb30a7eae880f202", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851f840759001825a8ce7553f89742fce", "guid": "bfdfe7dc352907fc980b868725387e9815ecd4a7aa21172f4cd0f852a7d65f91", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987aa7e4d277b83a6c2005d3bd41315c07", "guid": "bfdfe7dc352907fc980b868725387e982c61af41fbcab7f5eacd6fa54d09c6ae", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c21f3ba8272292aa72857cccbc57c7ef", "guid": "bfdfe7dc352907fc980b868725387e988111f4627cedb236480aec99458b99d9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98049b638c9217d0f74898cfea6cf2b196", "guid": "bfdfe7dc352907fc980b868725387e98df83bb7e8b051f90b409cadc4705ee05", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9f0033bd4b412ff7011c5f9f9655357", "guid": "bfdfe7dc352907fc980b868725387e98594cb3e273977089eaeb1fb060477b98", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec890cd8dad8e4c5ef9ba964239629d6", "guid": "bfdfe7dc352907fc980b868725387e98f622108e7192e83fb23fa667080aae16", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c67452735bca4978626222af11274ea0", "guid": "bfdfe7dc352907fc980b868725387e98b42e42472dfa234449e2b3d79e02ba09", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853f06fee25fa23e36072c77b8a40b361", "guid": "bfdfe7dc352907fc980b868725387e98bca52d1ee88876a6075011157c05aff7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a7abe378885d40b4a86921f5fb62b75", "guid": "bfdfe7dc352907fc980b868725387e982a9a0d6166630242414305658e2662f1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98595505aedc8ca68d45492d2a79d6a8c2", "guid": "bfdfe7dc352907fc980b868725387e988afa3506ae4705df16ce99bffa735621", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98989bb8715701f8282be9be61b00a6fb5", "guid": "bfdfe7dc352907fc980b868725387e9838086bc15200f970795275481412a959", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807677f03426d69ee86bbbdc9a890c42c", "guid": "bfdfe7dc352907fc980b868725387e98f6e601551c8e121153069af33cf2c307", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e358167824de744359e36e407e819d80", "guid": "bfdfe7dc352907fc980b868725387e9804aec833f32913b384575a2757c208eb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b990f3acef4e0686afbd54f3cbd3f0e", "guid": "bfdfe7dc352907fc980b868725387e985449deca96eef34d34cc2a155ca15ee5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e4b77596a727253b95473c671db014d", "guid": "bfdfe7dc352907fc980b868725387e987bd65d4bd69dcb35177a650263dbf82e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea01c8cde9d0813bf8691065ed3b94fe", "guid": "bfdfe7dc352907fc980b868725387e98381aee8f8313f33fa3073f7c8fe06531", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba006667ca75301340bdd206228be3c3", "guid": "bfdfe7dc352907fc980b868725387e98a2785785545e80cf50e1d46c3023b935", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98893830191ea338f02e2c1dc91b910130", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e50bce1ee627e5400cedb08c785cac16", "guid": "bfdfe7dc352907fc980b868725387e981ad5522df77aa07b9e666f14b171fc59"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec3fea074a093ce645f24cba496ffe24", "guid": "bfdfe7dc352907fc980b868725387e98303539ce1bb97d7420ae377057d0f603"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d19de463076821a66127d8e4575d317", "guid": "bfdfe7dc352907fc980b868725387e981a91beabee992caf190973fa935ac2af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981505227325473dacb41372db35a9601e", "guid": "bfdfe7dc352907fc980b868725387e98ff14f2693762e891519ed9e5fd5c3783"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860c443fdfd45bac36edd9d4eebd2a3a5", "guid": "bfdfe7dc352907fc980b868725387e9803f9ac868a6b2bd24e5ef6395565f481"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e60d5e4352ad855d0b0f1837fc6acfc", "guid": "bfdfe7dc352907fc980b868725387e9803fc5d79b5d3f9b0645e4d8058cbf826"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855fa0dfa4640881c48744327ba38973c", "guid": "bfdfe7dc352907fc980b868725387e98ce94cb090d3ff44cdaa7253ab93d3ae6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e59b35bf784621f1b9b7ce742447c708", "guid": "bfdfe7dc352907fc980b868725387e98a04e36f7a34f8b07f7af633feb3d07bb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892277df92362620dc837f4046e8eaa65", "guid": "bfdfe7dc352907fc980b868725387e9898e8eedbf771e119dce800e004bd6e15"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814b92abbd43827bdfdec856fa0609bbc", "guid": "bfdfe7dc352907fc980b868725387e98c935af5360363fa94ba9c557bdb5b702"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a8b00ee0edfbf6d5111dcf5ed66287d", "guid": "bfdfe7dc352907fc980b868725387e98f7f3d8af76f1642f368a6513747712f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822279c316c8d16dd40a5e73a621cfd08", "guid": "bfdfe7dc352907fc980b868725387e98a240787dba60d4308c3db54e2fd6273e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98112e8479b29a2252c9f22303f7272944", "guid": "bfdfe7dc352907fc980b868725387e9850927a23eee58897fd69be16bd2d55fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984405983a4f0e34b0444e9f2ae51759c9", "guid": "bfdfe7dc352907fc980b868725387e98c56e47d47ef6ebf6269e2404747f6167"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845fad3a9e0c39afe2167ee68d1ba43f1", "guid": "bfdfe7dc352907fc980b868725387e98fe426173b4dfc98aaec03429a66f9c14"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982793d1315e5f466ebeac1455f608941e", "guid": "bfdfe7dc352907fc980b868725387e98056d26ad1a9ae8e6551dc76e0e4d3b53"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98beae82254546ba281f0d574c1c34d53d", "guid": "bfdfe7dc352907fc980b868725387e98a8e7411cd41dd9d87ee9dfa391be1075"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8142c1d55e0fdb531a8436f761e1745", "guid": "bfdfe7dc352907fc980b868725387e9832590740e226bc3a672a3f79bff93a36"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a123117de874a81e5476a71ca3bcc83", "guid": "bfdfe7dc352907fc980b868725387e98c1f6bff733d188ed4b805d8988144310"}], "guid": "bfdfe7dc352907fc980b868725387e988f2159a3fa518201e99f45201240c014", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9834dfa23abd1ee3cbdf63f2e7e7c8692e", "guid": "bfdfe7dc352907fc980b868725387e98c55b2fd41ec59641b36bba517fd96ffa"}], "guid": "bfdfe7dc352907fc980b868725387e98f59d14b41d6065eb13a4af8fcfae4a69", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e983e9e224ef10dec5e1925539f36c732b7", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}], "guid": "bfdfe7dc352907fc980b868725387e98ef10255b706f98e1e88fae00855b0968", "name": "permission_handler_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f8f53f8ba4165e76c7481b24262177ed", "name": "permission_handler_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}