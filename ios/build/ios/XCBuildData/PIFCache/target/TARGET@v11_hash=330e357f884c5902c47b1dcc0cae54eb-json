{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98cbc1ac6994a983882192a55624108eea", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ddc57a65186d8e6f6b320e909e7dd906", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b6cd1188d7acf7ed6718d724cf5fb7e2", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983393eb9331845573a748f90d1742103d", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b6cd1188d7acf7ed6718d724cf5fb7e2", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b6d4a33c1cbbbfba67db279f242f8667", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9836f6a34f3bb48c9b1d49090dbb326970", "guid": "bfdfe7dc352907fc980b868725387e98db6ea792e452a961f7882faa9dabf4a0", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986e7c7f8fdf22b1979f502e836cd75ce3", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c3e6959cfb53d2f75acd6af582516564", "guid": "bfdfe7dc352907fc980b868725387e985ee5c25ae534ca9a878b45f3c509cc68"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b563ce988147d21919f2a3cf7df73227", "guid": "bfdfe7dc352907fc980b868725387e98d7da332d13487da616afee35c08ebb35"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846d1cfe7494f16e3bb987ae615b7de47", "guid": "bfdfe7dc352907fc980b868725387e985f7b02f88b1987ae01afc876b994286c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8974bdf5d5fdd89ecddd744907f6d95", "guid": "bfdfe7dc352907fc980b868725387e98569563bd6aac0d06c7cfdbafedfa0fc8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eecece1c7f96a2b52480947800939315", "guid": "bfdfe7dc352907fc980b868725387e98710956a7589190a2ac0b04b0e4555875"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982baf5d0c268e08fcf6d1cca5a76697fd", "guid": "bfdfe7dc352907fc980b868725387e983e3bae921aa9fdb88ff10decde516751"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2257a40906d073bde987a31683c154b", "guid": "bfdfe7dc352907fc980b868725387e980e6650d448bfde858547c7fc9b8d5c1c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853f390a3ab509e6ee0fd1ca101ffa9e1", "guid": "bfdfe7dc352907fc980b868725387e98637016df83855038ff73c834e59a1142"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832af6b676a60f267b9353530db2679b7", "guid": "bfdfe7dc352907fc980b868725387e98657230badaf16589a96fbbffe265675c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b7276fc68be8abd0d21e42a8026e343", "guid": "bfdfe7dc352907fc980b868725387e98be42697c2c2d4b1a48a867dd928b7296"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b971d7e89455dcc1f45881fd1707503", "guid": "bfdfe7dc352907fc980b868725387e98e202c356ec2d1ebd575595bd672bb89e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980be85b6a9aad8bd5950f1ad3adf8489d", "guid": "bfdfe7dc352907fc980b868725387e98d1e91b71c2cf70256c42bf2b632bf739"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f111bef26eab0bd0e61a85f95542953", "guid": "bfdfe7dc352907fc980b868725387e986b9711f46e8c2718bbb6f1cd1327ed6c"}], "guid": "bfdfe7dc352907fc980b868725387e98e13c0bf79c3d2eda2d350852bfcdd566", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9834dfa23abd1ee3cbdf63f2e7e7c8692e", "guid": "bfdfe7dc352907fc980b868725387e9841df2277791a5e8ba662cbd76a0f2776"}], "guid": "bfdfe7dc352907fc980b868725387e98c913253718b1a50c7a351f12aa6a17ef", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98efb42501920925b9f958f6755659eb69", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e98253d2065662a2cfa03688a002189b529", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}