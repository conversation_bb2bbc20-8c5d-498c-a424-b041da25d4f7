{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9809be37b0773a78ecc8a785a20c8aad26", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FMDB/FMDB-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "FMDB", "PRODUCT_NAME": "FMDB", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2"}, "guid": "bfdfe7dc352907fc980b868725387e983fb6714790cd1498e5bdff121d5a08bf", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986866e3354f0a83daeb278fbf3fa56243", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FMDB/FMDB-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "OTHER_LDFLAGS": "", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "FMDB", "PRODUCT_NAME": "FMDB", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES"}, "guid": "bfdfe7dc352907fc980b868725387e983a3f7f07c567e32f6ebbf18bb46e5e30", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986866e3354f0a83daeb278fbf3fa56243", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FMDB/FMDB-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "OTHER_LDFLAGS": "", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "FMDB", "PRODUCT_NAME": "FMDB", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES"}, "guid": "bfdfe7dc352907fc980b868725387e9839b8750636f32411d834ce45317d5104", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9837fd6792471ab41137c93dff0a00d09d", "guid": "bfdfe7dc352907fc980b868725387e987610e881cc02bf324f63582373a7e17d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f76ba532767516ca70f41b218531991", "guid": "bfdfe7dc352907fc980b868725387e987a1981a4db6797d04cb645af68c30ca0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d90a3e03e56d12a58ee50e71075f430d", "guid": "bfdfe7dc352907fc980b868725387e98cf12ae042e243298d0790882183a6a23"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b52e45abe822ebceb0bc598d5fe119f", "guid": "bfdfe7dc352907fc980b868725387e98c439020b3c52cabd0716bdc95b2003df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98354cedc88730281b61c620be4a455b67", "guid": "bfdfe7dc352907fc980b868725387e98b03613ca88f8131c884623056834b83c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be714a0462fc4376837718c75ff8b76c", "guid": "bfdfe7dc352907fc980b868725387e98282d7c074cecdc78eed6b80b411d372e"}], "guid": "bfdfe7dc352907fc980b868725387e98995c59b0d6bb28496f59f1031f4cf580", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e35e367b0bce22585b1457d7dab8cded", "guid": "bfdfe7dc352907fc980b868725387e985d4e80ff21601993f6ad2f9f4ea5b3f9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae462672519a60af98e585c8c3f9d613", "guid": "bfdfe7dc352907fc980b868725387e9833b8b9192fc86c6c216c1dc33073b019"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c85e837c889a1b802eee47701065846a", "guid": "bfdfe7dc352907fc980b868725387e985f867899e1aa321a2122083993837eb0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4ffb623310a40b278a7e6fcc6da8675", "guid": "bfdfe7dc352907fc980b868725387e986d965c0b8cce01524d29c448c0417d55"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d2fe3f4237e5b101a28ee107b930fe1", "guid": "bfdfe7dc352907fc980b868725387e989984b071e06331ed0eb7ba0439f106d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9ddf3e13d36fb29dfdb2a46f3c8662b", "guid": "bfdfe7dc352907fc980b868725387e988a043a90c246a23ddc5244a627e52ed4"}], "guid": "bfdfe7dc352907fc980b868725387e98e73bcacb8e76b1b3887e66f8a632e7d0", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9810bbb65ca790cade570796160c105414", "type": "com.apple.buildphase.frameworks"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98cab8b41b13f524e82b01b79420264253", "name": "FMDB-FMDB_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98e5199a3f70555689be7018ee7f9f878d", "name": "FMDB", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983a0b2834dab2d28f0f87c3e6cdedcc0d", "name": "libFMDB.a", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.library.static", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}