{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985a324dbf15a4a96dfeba252a79bb5376", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)"}, "guid": "bfdfe7dc352907fc980b868725387e986b894efc119e570edf57090da94e8b49", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ac7bd0015e02e72b83bcc95cf741d45c", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)"}, "guid": "bfdfe7dc352907fc980b868725387e98a8d42b74ae7d0c00ea52774f2b6dbd83", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ac7bd0015e02e72b83bcc95cf741d45c", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)"}, "guid": "bfdfe7dc352907fc980b868725387e98147d1e5d7b24b6e0ae221e5206d9e85a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9842ed0b0778f0223dfd1cd94d0adf1098", "guid": "bfdfe7dc352907fc980b868725387e989967be0f4c4e4fb6e62db8b9182dfa78"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b40de71808d775ec4fd2eb90d1a6f53c", "guid": "bfdfe7dc352907fc980b868725387e981cd658b0fc129109eff37d4fb8197a7f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886815878aae539db3f7c79fb3db69d4a", "guid": "bfdfe7dc352907fc980b868725387e98f28849ee52b233cbc9cd1271a8807abe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984bd4e2776d0fb8706e2c5f50c6cd0a7e", "guid": "bfdfe7dc352907fc980b868725387e98687541263b93bbb14f12a3223c5fe6b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815349723f915212e37f9156a591c766c", "guid": "bfdfe7dc352907fc980b868725387e98c4f276dba27ee16b750658ee2c073673"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98421e0db4f3e8dfd080b4a02b8ecd4590", "guid": "bfdfe7dc352907fc980b868725387e981aa7641e5f71f17ddab8bdd9e2efa5c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d75fea36ff875674fc6f53c75f9382ef", "guid": "bfdfe7dc352907fc980b868725387e98e79ff965fedc40105847e2c2a803062a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890a4925dbccb0805a68d5c91eac1e112", "guid": "bfdfe7dc352907fc980b868725387e98888fc52bb7103860b6be4d6ae2b2d458"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982828f6f6da8409848dcb6f6b57059f71", "guid": "bfdfe7dc352907fc980b868725387e98a4730a9f55c9b00c91ef4529c2914d53"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b049171c31a0eb8a030354ff1705773", "guid": "bfdfe7dc352907fc980b868725387e98628026fb7da5769f11ef14386e941bf3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1b4ffb4bdc5e9f321cbcd3a6a1583d3", "guid": "bfdfe7dc352907fc980b868725387e98c16a3d25c8433e1f4eab381eb0e978b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98101c15883a4c32427978c3dd8aec1bd1", "guid": "bfdfe7dc352907fc980b868725387e98412a97ea318743d256d9fd35f5efc7cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981310405d35e703673043090a5d90a06b", "guid": "bfdfe7dc352907fc980b868725387e982f74bd52d099b84726e141281ac119d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db02cabb4b2aed4e629a2f37d8668f62", "guid": "bfdfe7dc352907fc980b868725387e9847a01ddbcae5498ed8804b72fcc0bf3b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e76183a6f8165f252ef8537f7211d86c", "guid": "bfdfe7dc352907fc980b868725387e983bac629e1880c5357b7526ecf903d5c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98362efa8cd49917233bde5a64c774c296", "guid": "bfdfe7dc352907fc980b868725387e98efb8e314ae058f03cb5dcd58b9e41d23"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9c9bc948ff12d6c7ef7a2541349b711", "guid": "bfdfe7dc352907fc980b868725387e98e4afebaa2aa32c9e3e3d035c71806d6d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e879930787f35b81f4ca28e7d6b70828", "guid": "bfdfe7dc352907fc980b868725387e98d1cde0018c069bbcd3f0d3963b14a460"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98226892a9e0f21434fac5ccc350be4d12", "guid": "bfdfe7dc352907fc980b868725387e98e810029190c1d543536b49726f2b22ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c2dd5348ba2e3d7aec51ff549df0eca", "guid": "bfdfe7dc352907fc980b868725387e982f26dd5ec77701fa14b1e5fd76a48f1d"}], "guid": "bfdfe7dc352907fc980b868725387e98bbe2916aa06ffcfd656707f9b1e5d976", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98aea5252603324583a170c357c7a7b6a2", "guid": "bfdfe7dc352907fc980b868725387e981675410bd2f79adaf38b02ab1037abf9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c4f678784331681a1300d6bb6ec794b", "guid": "bfdfe7dc352907fc980b868725387e9808b61f533bc200eafc7fa6face0e11fb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec0ca861ab2f5f05de7c49483c76e06f", "guid": "bfdfe7dc352907fc980b868725387e98fddcf34a9d34b49462bd7c1b050c00b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b62fbd49477cc539b5ac35aaf364d04", "guid": "bfdfe7dc352907fc980b868725387e9862e729a477ff2cfe8c275ad51e896ffb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bcf64fcd4f324fe6b84d05dfeb3fec4c", "guid": "bfdfe7dc352907fc980b868725387e9852a537ddd67a344007914c03469b260f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f18e17a8e811d31b1956ffb950fc7992", "guid": "bfdfe7dc352907fc980b868725387e98f966b0c292b1383bfaf36b18ae1a2d31"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c297045765d3e5f78e383651a6cf1bb6", "guid": "bfdfe7dc352907fc980b868725387e983d153e62b2c62b9556a0f5eb58b7c09d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7370ba21311cb9dae108a3fc486ef41", "guid": "bfdfe7dc352907fc980b868725387e98ead3640b8e2f90576dcbe2fcca49d6e9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbc1a19c4fbebad0b1f3f975b8e7d7dc", "guid": "bfdfe7dc352907fc980b868725387e98768e3e39269371823af6bbfc592566d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f78ee011210cfb86727d543f278b7d8a", "guid": "bfdfe7dc352907fc980b868725387e98fff61f95d2eb2a4d62fb0f8233dc6132"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5fd25c2d91310e3e9bcd8918a2a6ef6", "guid": "bfdfe7dc352907fc980b868725387e98fa7d3e47c7eaa661f5e73bcc2fec361c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4a9e6205eb851b94c8b5185685076a3", "guid": "bfdfe7dc352907fc980b868725387e989dc9f10a2f242a19280f7652449cfa45"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d877a516fe036402384b43d99bfce785", "guid": "bfdfe7dc352907fc980b868725387e98ffc0c95fd068606b7e3bf02271827134"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986af449b0c4fcdc0526615ca8562362e8", "guid": "bfdfe7dc352907fc980b868725387e98d405b3fc400f5a64935efb7dc860df8b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985039c7271e5cd0d2f70dcf5614c3db57", "guid": "bfdfe7dc352907fc980b868725387e98a2b2ecae01914ae827344ba1cf13c75a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6eaf317d3665543e50910f3ca57bc40", "guid": "bfdfe7dc352907fc980b868725387e98233e6f4eb958a52b915461fd248b2b13"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981256c100f83258d2185b9052a758a6f2", "guid": "bfdfe7dc352907fc980b868725387e98f50a22de188f0f3d4c525c341c284aaf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825121465b1034694146f5666e4814072", "guid": "bfdfe7dc352907fc980b868725387e98bcdd2aa5536d0272a813690253ea3c35"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874cb58e2d60fa75f34043d7e98da61c7", "guid": "bfdfe7dc352907fc980b868725387e9858bb7122ba3b8b9adc914530e7af3f8a"}], "guid": "bfdfe7dc352907fc980b868725387e9879dbbb86b2c59fc96ff9c2155d0affd2", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9847d456404c6bf6f220c609fd5515a766", "type": "com.apple.buildphase.frameworks"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}], "guid": "bfdfe7dc352907fc980b868725387e98ef10255b706f98e1e88fae00855b0968", "name": "permission_handler_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f8f53f8ba4165e76c7481b24262177ed", "name": "libpermission_handler_apple.a", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.library.static", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}