{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98bfe6fba1e4298a95afbcf32099728aa6", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98fa17630ad66aa2a6e68de6623cbcc1fb", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98eed846eeddf7c42d254081f0a43ae351", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9888049bcd3ebec1d4a0c867b1242ff6b8", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98eed846eeddf7c42d254081f0a43ae351", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9807c1675f631c11be2d9efd18938af6ab", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c49ab5e6d4248d8e5b01663b5f00eb3e", "guid": "bfdfe7dc352907fc980b868725387e98a37ee7d82a4ce0130c4ba92952de1029", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dac2c411ce55fcf14177060ada3f0cb1", "guid": "bfdfe7dc352907fc980b868725387e98313e3101b9976bd3ce3ff8f1b89bdff9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f0409d72b243a5705d4378ef7810b5a", "guid": "bfdfe7dc352907fc980b868725387e98af6df5526e94e057e6523c835b050bd4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f93c97a477425dc26ea35cdacc8a73ac", "guid": "bfdfe7dc352907fc980b868725387e98e00c7102d08590bd390b874026913c9a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98943172c7856f7b21243ea5cade3b7521", "guid": "bfdfe7dc352907fc980b868725387e983184cb50cfaca0d2069702992bf20502", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847a0efb0eb720901408cbd5a4c5318cd", "guid": "bfdfe7dc352907fc980b868725387e98eb63259b5fe3cd020d3ec33ce88f75a5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8b8e068137ec8aa6cb99d8fccc6ae86", "guid": "bfdfe7dc352907fc980b868725387e98cc03d406ab34fbd79eae05d64ab679de", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3199cd31222478442ecd76f73eb2e7f", "guid": "bfdfe7dc352907fc980b868725387e980e9bca163d51f4d06c9f342191c00655", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b94df6a28adbfdc19f2d15a6492bc5b", "guid": "bfdfe7dc352907fc980b868725387e98f33b87b7b58903edae2d09bc56671d66", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db2226f004629b1b9ef0b8eab9f702c7", "guid": "bfdfe7dc352907fc980b868725387e98f180466011111637d45276071ea5652f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbc0d76d401f509d8a203b32f756381a", "guid": "bfdfe7dc352907fc980b868725387e9846105b3e2596a696d0b20e385c12f2eb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4d07c557b9418ee2c7cad1604bdb056", "guid": "bfdfe7dc352907fc980b868725387e98da8e211bade2cc3f56bfd5dbbac25c5d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98477f7a524537d820d4fded6da2f1c40a", "guid": "bfdfe7dc352907fc980b868725387e980d01d437843244b302448aff30a8502f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b300b80ad2519cac614d08eadd0bc4d8", "guid": "bfdfe7dc352907fc980b868725387e983bd02dde0b40a723a1904fb2f208158e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d414cc016fbef596810c9fc0438444c5", "guid": "bfdfe7dc352907fc980b868725387e982f67cff0d7720e82c7f5c2b3a1506c58", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd823726c9c096d4667bede99ec3861a", "guid": "bfdfe7dc352907fc980b868725387e9838520237cdb926dbbed00fa539df4567", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f11479241b29b73a028ac62507675ee", "guid": "bfdfe7dc352907fc980b868725387e985d715c7a19b210bc69119e4dfdb3d68a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc69ee8bb9335abaf0349fd6f0488358", "guid": "bfdfe7dc352907fc980b868725387e987c865a242208e2c279f7d85c619cb7b5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897c345910813b0dc5c692359ee705eb8", "guid": "bfdfe7dc352907fc980b868725387e9869ced4ac4c2fb7089644c5588fc3980c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835cf0d4c72b0e86c4cfb1d9c7ff8c16e", "guid": "bfdfe7dc352907fc980b868725387e986d2e5d229ef6a106a4d26a86e64d14f4", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811d3c52638ee6b64fe7b23f7376d52e1", "guid": "bfdfe7dc352907fc980b868725387e9856588cb065879bccf67c7cd4d544f4a7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989230daa9734665624a313087de5bfdf2", "guid": "bfdfe7dc352907fc980b868725387e98f28c3a1e2f6250dcd8b4d767b7a3f7a9", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986726ff12e03035cd31fb11ec5c52bee9", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985852c1d7487a5bdee84d8e009643caf1", "guid": "bfdfe7dc352907fc980b868725387e987c38a5da9ad8973c62def19741c96351"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fda82aab04fd2834ed0b018b87f2e6be", "guid": "bfdfe7dc352907fc980b868725387e985647fc023bd834d819f65da2360cfb7f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985dae7ff77b986febee14c3642c39d4d3", "guid": "bfdfe7dc352907fc980b868725387e9821a22ad47dfdabf999c645b28f3085ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d040a0c99168d6164855bce15e35dfdd", "guid": "bfdfe7dc352907fc980b868725387e984a0e005603bef524841dd714ebce14bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b311db8cd13667ad3907a75cdba5dd20", "guid": "bfdfe7dc352907fc980b868725387e980f153828b0fa202da290b26a9dd5e4a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816349e06345156469d00b5c8a3236e8c", "guid": "bfdfe7dc352907fc980b868725387e983b257f3ac8d0b77e6ce7b5541c0f5d56"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5499eaa4372de0247999a19ee5381da", "guid": "bfdfe7dc352907fc980b868725387e989194f8fb5edfe5fb3769f4f990154c27"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e307950f5a7801e78cc417bb2cdd9975", "guid": "bfdfe7dc352907fc980b868725387e98f663ca00780b0299c4b07e0044d77561"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98faf8cd33b503d3c89c1013c0ebd98114", "guid": "bfdfe7dc352907fc980b868725387e984286a17f0af864ec37aa0a3b3ad00430"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98afcdc14b8258653042a2cd2118f9764a", "guid": "bfdfe7dc352907fc980b868725387e98391ee118b5a803c8cad053a40e4fa05f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6d37b2b67545d07104fec1004d67e4d", "guid": "bfdfe7dc352907fc980b868725387e98ad4785394c0e9d72c37c65e7e560bdec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888bd4ef8da55e385b203728cde8af17f", "guid": "bfdfe7dc352907fc980b868725387e988912ecdd34b4e771015be0f1324550da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835e2a37f70f85a9839dda424e8aa3595", "guid": "bfdfe7dc352907fc980b868725387e98e0ea0854ccca21bb96b084abedbdeb0b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f2bb3c1678759e70c87970cd7e1c19b", "guid": "bfdfe7dc352907fc980b868725387e9808b1c5174b0d71485bf5ee7dcc5686bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2cdfe0bcd7f5104193060809016dc2b", "guid": "bfdfe7dc352907fc980b868725387e98fe8ef319aa344a75004fd44eb2b2ac0c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb04caf9281c0f6684f8f8f839a22694", "guid": "bfdfe7dc352907fc980b868725387e980470fae40b7016f86e823d39a3a91df0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98418beb88d778d368522c07fdbd020126", "guid": "bfdfe7dc352907fc980b868725387e98953c527b0120b7152a7ec0d6093eae8a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bcf9194f4319cfad39483770e7277333", "guid": "bfdfe7dc352907fc980b868725387e98150c3268d954c3d4c1bac701bb91c77d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980fa6c5ff375588cee1aeaf123ec00318", "guid": "bfdfe7dc352907fc980b868725387e98e564536febfa0ed697c2acecc6098a61"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820cb619b5555e8489ba0a35fe4657116", "guid": "bfdfe7dc352907fc980b868725387e98be1cb289dab73448e72f39ebf24f840c"}], "guid": "bfdfe7dc352907fc980b868725387e982ccfcd59dfa6dd7191116ebced5458fc", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9834dfa23abd1ee3cbdf63f2e7e7c8692e", "guid": "bfdfe7dc352907fc980b868725387e983cdee3f4b11cf06fb4884dc2cf9be894"}], "guid": "bfdfe7dc352907fc980b868725387e98e5e1689a914d38586557223a61632c04", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98b43587444f3655fdc58e2320fd87524d", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e9823c5f71c7d1b08f67ed3333d41576d9a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}