{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9871373e2e38b552bd57b00aa661235bab", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a28c6eaa9339a5e38fa96dce2f400f8e", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9886e59a59bdf7868aeadec22c742776ce", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9817055aed28b077f12586902e8fcfc5d6", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9886e59a59bdf7868aeadec22c742776ce", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985bae1f5c9e1679f144a83f4dbc6b2759", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983baf95a9ab92a1269dfa0bf000876028", "guid": "bfdfe7dc352907fc980b868725387e987980a086be81498410e83fbc7de0a34b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d8a53a043bfd6e7b314b7862d47d656", "guid": "bfdfe7dc352907fc980b868725387e98bd88ae0446ab94ccf5935a5e757c5172"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983451da78324fbf44668c10c5ab875e67", "guid": "bfdfe7dc352907fc980b868725387e987293c3751bd6c44c77f41ded5077b8b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d140f6ec323ae32436f74c860dbeef12", "guid": "bfdfe7dc352907fc980b868725387e984f3829f9162b41d91410ebf01065db1c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb0d7d6bac8df4d1400dacb85bf10ada", "guid": "bfdfe7dc352907fc980b868725387e98917c18247ae1ff80fc9a0fedfd00ca10"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98883f7a4cd81929217800d835c08d850d", "guid": "bfdfe7dc352907fc980b868725387e98c6be2df8939af561d74097316496d3ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984803cd531472aee184742e423f5f351b", "guid": "bfdfe7dc352907fc980b868725387e98a6bdde2f9a4d9b7d00fc37bb47b71115"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981520d06cc9dfd9845ac8f2b41ae0b023", "guid": "bfdfe7dc352907fc980b868725387e9876b7b87e058d9870e46a07e23553ef7f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987f5198efc2de7f6cfd77a36df7f00f51", "guid": "bfdfe7dc352907fc980b868725387e98d64be4108cb3ae8ae459db288d28e0d1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc6136f96600ff762bb38eb16693efb3", "guid": "bfdfe7dc352907fc980b868725387e98916039be5cf50f428deae51b1f04936d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fccfef0e1dc6aab2fdb9d22948646de3", "guid": "bfdfe7dc352907fc980b868725387e98fe98502d983135b76e4efa7c38926181"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c274ef84966b9db319c8b9d9f675655", "guid": "bfdfe7dc352907fc980b868725387e983d9b05a62481fea33d7ac33a6734ac40", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf6d53e96dbabb1fc27056fbe0f9e08f", "guid": "bfdfe7dc352907fc980b868725387e98a3e6e994cca259f96ea4ffaf9dc6a68c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9e174e1d2598272d3b58130bae9d3d7", "guid": "bfdfe7dc352907fc980b868725387e98bd402fefc48d4ad29434fea6bb6ecd0d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7faf84730bf618178a0312659a71368", "guid": "bfdfe7dc352907fc980b868725387e982ae88c02660ecd196457a9f9d215860c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f66071d96495f4dd0519b8dae097b72", "guid": "bfdfe7dc352907fc980b868725387e98444ed58fc8ce716879766a8c90cc2713"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd01dabb977f7863fe8262cf22ff84d4", "guid": "bfdfe7dc352907fc980b868725387e98e07333ad8a04d8d79980feeae77bfa58", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ea9e0189562e32575f2532764fc2515", "guid": "bfdfe7dc352907fc980b868725387e9875890804be7e2d639f23a76bf09a54ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c18bec0409571910bfbf95886ede3ca", "guid": "bfdfe7dc352907fc980b868725387e981994731715c5e87152eba257c15c0110"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986047cb063d99d3285e96c8a30ff1bf21", "guid": "bfdfe7dc352907fc980b868725387e988b3857f4d0a66dd66b89bc5510334f42"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985436c4a9a3cb7fea6de1ad157efebf54", "guid": "bfdfe7dc352907fc980b868725387e98607736c2cb1ef7c3a798ec122f85560f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98815381062a0f383c598fdd2ec868ed6d", "guid": "bfdfe7dc352907fc980b868725387e98f44c73c1f36c3a013d0d491e333f4ae3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984341a9738f5de5896b220af0f7b9070e", "guid": "bfdfe7dc352907fc980b868725387e981d68b4fe5e4240e0274296c66c6d46e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854d4c803406b55881546656e16c6849b", "guid": "bfdfe7dc352907fc980b868725387e98375540f408743cb958e93e734632f308"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809849ba7cf016d024bf81291bd2f2fd7", "guid": "bfdfe7dc352907fc980b868725387e989df4e265dd6703531b4994b5a3443afb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9808235958328561acee3a14570d95611b", "guid": "bfdfe7dc352907fc980b868725387e98d1e9e355054f4914ac89f4f769474bfc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ce2302c0bf546621ad90656147c86f5", "guid": "bfdfe7dc352907fc980b868725387e985dd3e24b073271f8fd4adee4bea7fb63"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984fd9c2436c50126d3a70016283d07f69", "guid": "bfdfe7dc352907fc980b868725387e987673a8caa91fb7efe94a8983504f6cf5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce7ea8ebfb7f5f8ded1ce3dc48b51e71", "guid": "bfdfe7dc352907fc980b868725387e98c47dd3f9f5d87adda62af21b44770bf5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9e6b54b113e543c6683eb66c718eb2c", "guid": "bfdfe7dc352907fc980b868725387e9855c590911b66672b5686ea46f15fc0f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983451e3bac85872ca84a593057ba891c4", "guid": "bfdfe7dc352907fc980b868725387e9844f39b477d4168792beef6e5b7b5fd5d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4f01096d6e5af22c676956b56fab7e2", "guid": "bfdfe7dc352907fc980b868725387e9817faf63b20748cd9b16403b032f2b6ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987967b77c9a06b87e10440ae3486563bf", "guid": "bfdfe7dc352907fc980b868725387e98fa1a5caaafa8a68aed668efbdbcebe2f"}], "guid": "bfdfe7dc352907fc980b868725387e98eb46903363e84b4900d500fee85a36ee", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9860e579921d0e14343409e1768cab1ac1", "guid": "bfdfe7dc352907fc980b868725387e984366a963824f9acc6fcc3ff1da0b74bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98146ba300d8c80608e2811a608064d8ab", "guid": "bfdfe7dc352907fc980b868725387e98303746fbfb1d62b74384a2e93037864f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b75009e277864cd26d6e3eba82abb9e0", "guid": "bfdfe7dc352907fc980b868725387e98b75f18979d19b6dfd587f1d90022156d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d86e34225c2103926b863e7546d551cc", "guid": "bfdfe7dc352907fc980b868725387e98b1ea67f30072d85a01ba280d8ada441d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98951b5aab1147e3febc851f043911568a", "guid": "bfdfe7dc352907fc980b868725387e989bb173c06045746e992c4ce397135d6f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885a5fdc8ec767ef361bea40d316c985c", "guid": "bfdfe7dc352907fc980b868725387e98602d915ac865ec0451b1b6429794f992"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f0c24815c83470cfdd77b20f901b285", "guid": "bfdfe7dc352907fc980b868725387e98463b369d11a1d3dc7f1471caedd5734f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c10a77da1b2d2b729f853cc5a6caa45", "guid": "bfdfe7dc352907fc980b868725387e987c61097b90abdfbef8213bb4d7f39b0f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d7be8833e885304ba99f76fe4eb203b", "guid": "bfdfe7dc352907fc980b868725387e9844bd3963b51b7fe6c4ce5c7828ffcc4f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1b92f806db56cc6487c40b40239b63a", "guid": "bfdfe7dc352907fc980b868725387e9873d6db0355c5abe67287e11701793f0b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818958b0cebc8d7893d9faa2c6b4fe161", "guid": "bfdfe7dc352907fc980b868725387e989de67cb34391d4dc3e861ce6faa4d06e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815de4ae6c89f9fd3da47db742f3526e1", "guid": "bfdfe7dc352907fc980b868725387e980db46c7febaebf4edf459e78793a6eb6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f12cf6a226428f7891d6883a9875fd97", "guid": "bfdfe7dc352907fc980b868725387e98b6fe76971328e143fd9dd735efcabc06"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885bbe29b2f634214deb282497169fa89", "guid": "bfdfe7dc352907fc980b868725387e98078d79812e715347b010ab8cd427b2a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987fd4d812ed8fbba63a6bab5584b77d00", "guid": "bfdfe7dc352907fc980b868725387e983b9578b3fead3e7bb073ea3f7077817b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980db3fbac5d39a963803e58be7592c1be", "guid": "bfdfe7dc352907fc980b868725387e98824fadf44b65179bb4004c44671ccdb3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9855c64b8576b8beb320c4f0d4c59b0816", "guid": "bfdfe7dc352907fc980b868725387e98784a2d7985ee5527e214413e335c5ca0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0ccaae9a814dac362b8fa42286008e8", "guid": "bfdfe7dc352907fc980b868725387e9809def6d32524eab51ade542896c7808c"}], "guid": "bfdfe7dc352907fc980b868725387e98c10d29ce44ca4e3cf8e095718b80fe29", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9834dfa23abd1ee3cbdf63f2e7e7c8692e", "guid": "bfdfe7dc352907fc980b868725387e98e6fe42859624b184c359e1a1f84a0c0f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a22b70908b57b2cde3e263c1645f2bb4", "guid": "bfdfe7dc352907fc980b868725387e98a5abf08c8884c176f7e6e9e5827eed6b"}], "guid": "bfdfe7dc352907fc980b868725387e9887e672d8f56f88ee8141ac5262c6bffe", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e981f3f699ab7956b87381883c44baf22fe", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e9834eca9fcaffec7634b639ed31f48678f", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}