{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980f3a1e539a878351ebc86a71dc87a282", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2"}, "guid": "bfdfe7dc352907fc980b868725387e981803333a4d54677e58adbddd7afdfead", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9894af0226fb5a6b2e949ce1fdf9fcd74e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "OTHER_LDFLAGS": "", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES"}, "guid": "bfdfe7dc352907fc980b868725387e985a65927090604b11dee850cefb77dd72", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9894af0226fb5a6b2e949ce1fdf9fcd74e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "OTHER_LDFLAGS": "", "OTHER_LIBTOOLFLAGS": "", "PRIVATE_HEADERS_FOLDER_PATH": "", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "PUBLIC_HEADERS_FOLDER_PATH": "", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES"}, "guid": "bfdfe7dc352907fc980b868725387e9896393739773d9d19e77165512e3f7d38", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98fd30b26ef9022bd313434c5d0fd04894", "guid": "bfdfe7dc352907fc980b868725387e9815f7ee6c4ab339d47a14cec81ec988a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98259271446f93a7ff1dd5689a9a863178", "guid": "bfdfe7dc352907fc980b868725387e985740788f85e880c9cc2c4ed660e5d56c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c121f15083e0555179d9cf25b7441ca6", "guid": "bfdfe7dc352907fc980b868725387e98ae9faf8a84a5f9f0e6f4d0c30c00e62e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d7d9c8a4e8bbf169b9d664f7b4554a4", "guid": "bfdfe7dc352907fc980b868725387e988aa54d9beea03af13820e0f124246a0e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af5d9ab905de04a54b294179a65e94ac", "guid": "bfdfe7dc352907fc980b868725387e980dddc7827c5e27f9fb130f57551aa2dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a92d1820a38d1182266e0bd7ac004fba", "guid": "bfdfe7dc352907fc980b868725387e988d7648ab77ad6a9d86897187207e0647"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986dc2701a3f748113e3dace22dc8a0ea3", "guid": "bfdfe7dc352907fc980b868725387e98f57990db786ce0e277bf6a5a56fd2be9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830dcb31d5bda756de7c6e90f01d8c682", "guid": "bfdfe7dc352907fc980b868725387e980d461f29ab64fa6b6d6fcb9bc43910a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982faa4619a14199364007e97649a9cbd8", "guid": "bfdfe7dc352907fc980b868725387e982856a245f607f99a39f7bde476df8f25"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de5b12292c50d4d2974e66958ff0cffe", "guid": "bfdfe7dc352907fc980b868725387e980087da419510ba2e62621ea42223bbb0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cee088d513dee2dff23f7551df2a4fa", "guid": "bfdfe7dc352907fc980b868725387e98b849833df0eb5318f75728b7ffcf7609"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986cc11275363681049664f146761cbd68", "guid": "bfdfe7dc352907fc980b868725387e98f510d42d866c284d71ac943fc0e056cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a6dfc456908e232e50a3502986bdc26", "guid": "bfdfe7dc352907fc980b868725387e98849aa647d58cf47134ee6f25979010e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d966426fc1fcb0a035162e36bca3dd52", "guid": "bfdfe7dc352907fc980b868725387e98fe42e104f9bfbd6fdd07a6f7d4449dc0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e4fe813052311ce3cc3ca316a6573d2", "guid": "bfdfe7dc352907fc980b868725387e986a9e3349e077af7971617872bc5803ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815a5df607c3c8cdc2dc475909391b1e5", "guid": "bfdfe7dc352907fc980b868725387e98e913e950d0ae9ee942a45cca1c1a05c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9871db8a42cf21d18f84cd967b71f01fab", "guid": "bfdfe7dc352907fc980b868725387e9830d2c9fc83daa05bf5c00b8ddadabede"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984209ea3b23dead18f52fe97e37417e7c", "guid": "bfdfe7dc352907fc980b868725387e98d9a85d8ef9a1cdd6aeca729a0084d07f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839b22e7e21c0b8082fae37c3b352b8ce", "guid": "bfdfe7dc352907fc980b868725387e9883a8037ca95dcc14160e3defdb94430f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c99d4ca91be26d6ab30b099600e037f", "guid": "bfdfe7dc352907fc980b868725387e981161bf21ed9e27bf2a43e5fb818f9eb2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d785ff1f3869db9894a447a9ca3b6ed", "guid": "bfdfe7dc352907fc980b868725387e9862db4578c8dd733c6fc2b99b4d532a06"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a155901b3cc31bdc335b4c07424598e9", "guid": "bfdfe7dc352907fc980b868725387e98c46198979d2ef4fcf20455c54401e73d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894f08e38521c3b975e4aad290ede1f20", "guid": "bfdfe7dc352907fc980b868725387e9818a19c8a06d4d97e0de11d4086a70af2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982bae3c9ec39e1e2dd1901d1d149d7b41", "guid": "bfdfe7dc352907fc980b868725387e982e0cc253b0a8c4380dc3c4e920bdc790"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be7070a098a053b42f934ab989222bcf", "guid": "bfdfe7dc352907fc980b868725387e988b1bec8e52f77f6e41d9d10c153ce8c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e406c72efe2ad135bede5ea695a9ea4f", "guid": "bfdfe7dc352907fc980b868725387e988cbe884e950edd7938aa2c1a18d0408d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1facfa64a1fe39a9fc52a9873e0d948", "guid": "bfdfe7dc352907fc980b868725387e984c85381e05846b330d1c7f4cb5edf4aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824ef239f34c5bb45638fdd80d837c67e", "guid": "bfdfe7dc352907fc980b868725387e9846d00d803da13386e1dfc36c991770ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98106bfcdc085f49cc8017c09003b0bb8f", "guid": "bfdfe7dc352907fc980b868725387e981ff8f938d24637fafb883650decf1b04"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880fb23e978a1f4816f53eefcc790953e", "guid": "bfdfe7dc352907fc980b868725387e983c2819350d4386bcca2b9a708fad6253"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc58ff4b1285f0dd9de5ebce8d7cdbba", "guid": "bfdfe7dc352907fc980b868725387e98c3bbdea459197100739e7269dc4406da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ea1d538eaf255275f4f7b8485e97595", "guid": "bfdfe7dc352907fc980b868725387e9896a11198b310d3571c0dc72225bb2e7d"}], "guid": "bfdfe7dc352907fc980b868725387e9883862b4875a8397564f100d4a55391c4", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9825f589b0b023795fb3249fecbe6ad12a", "guid": "bfdfe7dc352907fc980b868725387e98deba4f94dd6522446ab6b09df6a8fac0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802c37952146552b76afad5478a8e7a5e", "guid": "bfdfe7dc352907fc980b868725387e9814ef2366b801e8d9fada334a64501a3d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e02da387f17e221314894c142a1ee18", "guid": "bfdfe7dc352907fc980b868725387e98bbd5b28cecdc0dcc31052b869520fa27"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842fcfd489d14c1b6ff010f6303ed5afc", "guid": "bfdfe7dc352907fc980b868725387e98340401ac4f61815c29784a46daf17246"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aee914b8b5e5ad44a4c36df0d2c4c802", "guid": "bfdfe7dc352907fc980b868725387e9840c463a51ac1cae652cbfc5c5edaaa19"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4b81e51a590a7ab7f1450b240672eb3", "guid": "bfdfe7dc352907fc980b868725387e98162c6431dd804668ec258077c6d9901e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821549ea7972e62443a7013bc455b43b3", "guid": "bfdfe7dc352907fc980b868725387e9825d1faabc9fa3131041903f4e6a60078"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9e4f7321b38c96dc58f5856d6b01654", "guid": "bfdfe7dc352907fc980b868725387e989074364f4e04a49306187023e4426678"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842b6ecf4c036adab4abcbcef9c48dbb6", "guid": "bfdfe7dc352907fc980b868725387e98ac2eb0a63cba893879e0bc38d24dac59"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98885c48538bd7540f3f813636df72a429", "guid": "bfdfe7dc352907fc980b868725387e98911c19218fe5f0e2777fceaad860d884"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987423aef9622c8b2f1cb1baa3175564ac", "guid": "bfdfe7dc352907fc980b868725387e983f082abfe90052243a72cd43cc5c435c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a44660d1b99ae86c8f94d8b36356e83f", "guid": "bfdfe7dc352907fc980b868725387e98036a6e1d1fd324065bcf70215c7ff73d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9eff9655fba53220d89aca4b8b4b68c", "guid": "bfdfe7dc352907fc980b868725387e98d2de28ad5b381e49efec25a6667ee307"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f15e1a7813e60476c3ab73b89c71d401", "guid": "bfdfe7dc352907fc980b868725387e98cbdb23e56662c79aa912e14416ae0dd2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834bb4dbfc2b8c184cd97c42d3d29461b", "guid": "bfdfe7dc352907fc980b868725387e9838fd5053c9ecf552b12e0a8c82cd8686"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982aa904204171c4457389cac81fc4f9e3", "guid": "bfdfe7dc352907fc980b868725387e98d87821a3960e8b9e849a42f2169af2bb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8e511c4aa82c951a30f76329b18149c", "guid": "bfdfe7dc352907fc980b868725387e986a97ebd9f40fc4dcd37c01326265f450"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981bbd7347498a34f4332976a10254cc79", "guid": "bfdfe7dc352907fc980b868725387e98726dbe4755e8326b94e7e7b8af0affb8"}], "guid": "bfdfe7dc352907fc980b868725387e9867acab00997d69cefbb3d6ef9800f7f3", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e981f8458563afcb922a40d7aa0dc03d387", "type": "com.apple.buildphase.frameworks"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "libFirebaseInstallations.a", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.library.static", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}