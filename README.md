# ju_medicine

A new Flutter project.

## Getting Started

This project is a starting point for a Flutter application.

A few resources to get you started if this is your first Flutter project:

- [Lab: Write your first Flutter app](https://flutter.dev/docs/get-started/codelab)
- [Cookbook: Useful Flutter samples](https://flutter.dev/docs/cookbook)

For help getting started with Flutter, view our
[online documentation](https://flutter.dev/docs), which offers tutorials,
samples, guidance on mobile development, and a full API reference.



List of future adjustment:
1- Sliding and absorb button look at youtube video
2- Icon images from internet download (Done)
3- Shared Prefrences check if subyear exist (DONE) But optimize Transition
4- SplashScreen and longer EntryApp time. (DONE)

5- drawer slide + gesture absorb + active item
6- open (pdf, docx, etc) files with external apps if link is a file or open url in browser
7- download added to notification with progress (downloaded in JUmedicine Folder)


final
8- notifications with firebase and APN subscribe to topics (Years) and unsubscribe when change year + sqlite

9-drawer on different orientation is wrecked


***Reusable***
1- actionButton in popupcard
2- dropdown menu in settings and first activity


***
*Notes*
On Android urls need to be HTTPS to be opened in app of application
***


****
Testing functionality
1-open urls (cards)
2-download (cards)
3- change batch 
4- change semseter



Notifications
1- topics
2- counter (unread) 
****